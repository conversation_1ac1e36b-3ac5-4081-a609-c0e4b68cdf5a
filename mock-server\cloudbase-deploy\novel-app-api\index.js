const fs = require('fs');
const path = require('path');
const tcb = require('@cloudbase/node-sdk');

// 初始化CloudBase SDK
const app = tcb.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 读取数据库文件
function loadDatabase() {
  try {
    const dbPath = path.join(__dirname, 'data', 'db.json');
    if (fs.existsSync(dbPath)) {
      const dbContent = fs.readFileSync(dbPath, 'utf8');
      return JSON.parse(dbContent);
    }
  } catch (error) {
    console.error('Failed to load database:', error);
  }

  // 如果无法读取数据库，返回默认数据
  return {
    users: [
      {
        id: 'test-user-id',
        username: 'testuser',
        passwordHash: 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae',
        phoneNumber: '13800138000',
        email: null,
        avatar: null,
        isMember: false,
        memberExpireTime: null,
        membershipType: 'none',
        isPermanentMember: false,
        isDataSyncEnabled: true,
        settings: {
          enableBiometric: false,
          autoSync: true,
          enableNotification: true,
          theme: 'system',
          language: 'zh-CN'
        }
      }
    ]
  };
}

// 标准化会员类型，确保符合Flutter应用的枚举值
function _normalizeMembershipType(membershipType) {
  if (!membershipType) return 'none';

  switch (membershipType.toLowerCase()) {
    case 'free':
    case 'none':
      return 'none';
    case 'monthly':
    case 'month':
      return 'monthly';
    case 'permanent':
    case 'lifetime':
    case 'forever':
      return 'permanent';
    default:
      return 'none';
  }
}

// 发送短信验证码（需要集成真实的短信服务）
async function sendSMS(phoneNumber, verificationCode) {
  console.log(`[模拟短信] 发送验证码到 ${phoneNumber}: ${verificationCode}`);

  // 方案1: 腾讯云短信服务集成（推荐）
  // const tencentSMS = require('tencentcloud-sdk-nodejs');
  // const SmsClient = tencentSMS.sms.v20210111.Client;
  //
  // const clientConfig = {
  //   credential: {
  //     secretId: process.env.TENCENT_SECRET_ID,
  //     secretKey: process.env.TENCENT_SECRET_KEY,
  //   },
  //   region: "ap-beijing",
  //   profile: {
  //     httpProfile: {
  //       endpoint: "sms.tencentcloudapi.com",
  //     },
  //   },
  // };
  //
  // const client = new SmsClient(clientConfig);
  // const params = {
  //   PhoneNumberSet: [phoneNumber],
  //   SmsSdkAppId: "您的短信应用ID",
  //   SignName: "您的短信签名",
  //   TemplateId: "您的模板ID",
  //   TemplateParamSet: [verificationCode],
  // };
  //
  // try {
  //   const result = await client.SendSms(params);
  //   return { success: true, result };
  // } catch (error) {
  //   console.error('腾讯云短信发送失败:', error);
  //   return { success: false, error };
  // }

  // 方案2: 阿里云短信服务
  // const Core = require('@alicloud/pop-core');
  // const client = new Core({
  //   accessKeyId: process.env.ALIBABA_ACCESS_KEY_ID,
  //   accessKeySecret: process.env.ALIBABA_ACCESS_KEY_SECRET,
  //   endpoint: 'https://dysmsapi.aliyuncs.com',
  //   apiVersion: '2017-05-25'
  // });
  //
  // const params = {
  //   PhoneNumbers: phoneNumber,
  //   SignName: '您的签名',
  //   TemplateCode: '您的模板代码',
  //   TemplateParam: JSON.stringify({ code: verificationCode })
  // };
  //
  // try {
  //   const result = await client.request('SendSms', params, { method: 'POST' });
  //   return { success: result.Code === 'OK', result };
  // } catch (error) {
  //   console.error('阿里云短信发送失败:', error);
  //   return { success: false, error };
  // }

  // 当前是模拟实现，开发阶段使用
  // 生产环境请替换为上述真实的短信服务集成
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟90%的成功率
      const success = Math.random() > 0.1;
      resolve({
        success,
        message: success ? '模拟发送成功' : '模拟发送失败',
        debugInfo: `验证码: ${verificationCode}`
      });
    }, 1000); // 模拟网络延迟
  });
}

// 数据同步核心函数

// 上传用户数据到CloudBase
async function uploadUserData(userId, syncData) {
  const results = {};

  try {
    // 同步用户设置
    if (syncData.userSettings) {
      console.log('同步用户设置:', userId);
      const settingsData = {
        userId: userId,
        settings: syncData.userSettings,
        updatedAt: new Date().toISOString()
      };

      // 查找现有设置
      const existingSettings = await db.collection('userSettings').where({
        userId: userId
      }).get();

      if (existingSettings.data.length > 0) {
        // 更新现有设置
        await db.collection('userSettings').doc(existingSettings.data[0]._id).update(settingsData);
        results.userSettings = { action: 'updated', count: 1 };
      } else {
        // 创建新设置
        await db.collection('userSettings').add(settingsData);
        results.userSettings = { action: 'created', count: 1 };
      }
    }

    // 同步用户收藏
    if (syncData.userFavorites && Array.isArray(syncData.userFavorites)) {
      console.log('同步用户收藏:', userId, '数量:', syncData.userFavorites.length);

      // 删除现有收藏
      const existingFavorites = await db.collection('userFavorites').where({
        userId: userId
      }).get();

      for (const favorite of existingFavorites.data) {
        await db.collection('userFavorites').doc(favorite._id).remove();
      }

      // 添加新收藏
      let addedCount = 0;
      for (const favorite of syncData.userFavorites) {
        await db.collection('userFavorites').add({
          userId: userId,
          ...favorite,
          syncedAt: new Date().toISOString()
        });
        addedCount++;
      }

      results.userFavorites = { action: 'replaced', count: addedCount };
    }

    // 同步用户历史记录
    if (syncData.userHistory && Array.isArray(syncData.userHistory)) {
      console.log('同步用户历史:', userId, '数量:', syncData.userHistory.length);

      // 删除现有历史记录
      const existingHistory = await db.collection('userHistory').where({
        userId: userId
      }).get();

      for (const history of existingHistory.data) {
        await db.collection('userHistory').doc(history._id).remove();
      }

      // 添加新历史记录
      let addedCount = 0;
      for (const history of syncData.userHistory) {
        await db.collection('userHistory').add({
          userId: userId,
          ...history,
          syncedAt: new Date().toISOString()
        });
        addedCount++;
      }

      results.userHistory = { action: 'replaced', count: addedCount };
    }

    // 同步长篇小说
    if (syncData.novels && Array.isArray(syncData.novels)) {
      console.log('同步长篇小说:', userId, '数量:', syncData.novels.length);

      try {
        let addedCount = 0;
        let updatedCount = 0;

        for (const novel of syncData.novels) {
          try {
            // 检查小说是否已存在
            const existingNovel = await db.collection('novels').where({
              userId: userId,
              id: novel.id
            }).get();

            const novelData = {
              userId: userId,
              ...novel,
              syncedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            if (existingNovel.data.length > 0) {
              // 更新现有小说
              await db.collection('novels').doc(existingNovel.data[0]._id).update(novelData);
              updatedCount++;
            } else {
              // 创建新小说
              await db.collection('novels').add(novelData);
              addedCount++;
            }
          } catch (novelError) {
            console.error('同步单个小说失败:', novel.id, novelError);
            // 继续处理其他小说
          }
        }

        results.novels = { action: 'synced', added: addedCount, updated: updatedCount };
      } catch (error) {
        console.error('同步长篇小说失败:', error);
        results.novels = { action: 'failed', error: error.message };
      }
    }

    // 同步短篇小说
    if (syncData.shortStories && Array.isArray(syncData.shortStories)) {
      console.log('同步短篇小说:', userId, '数量:', syncData.shortStories.length);

      try {
        let addedCount = 0;
        let updatedCount = 0;

        for (const story of syncData.shortStories) {
          try {
            // 检查短篇是否已存在
            const existingStory = await db.collection('shortStories').where({
              userId: userId,
              id: story.id
            }).get();

            const storyData = {
              userId: userId,
              ...story,
              syncedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            if (existingStory.data.length > 0) {
              // 更新现有短篇
              await db.collection('shortStories').doc(existingStory.data[0]._id).update(storyData);
              updatedCount++;
            } else {
              // 创建新短篇
              await db.collection('shortStories').add(storyData);
              addedCount++;
            }
          } catch (storyError) {
            console.error('同步单个短篇失败:', story.id, storyError);
            // 继续处理其他短篇
          }
        }

        results.shortStories = { action: 'synced', added: addedCount, updated: updatedCount };
      } catch (error) {
        console.error('同步短篇小说失败:', error);
        results.shortStories = { action: 'failed', error: error.message };
      }
    }

    // 同步小说章节
    if (syncData.novelChapters && Array.isArray(syncData.novelChapters)) {
      console.log('同步小说章节:', userId, '数量:', syncData.novelChapters.length);

      let addedCount = 0;
      let updatedCount = 0;

      for (const chapter of syncData.novelChapters) {
        // 检查章节是否已存在
        const existingChapter = await db.collection('novelChapters').where({
          userId: userId,
          novelId: chapter.novelId,
          id: chapter.id
        }).get();

        const chapterData = {
          userId: userId,
          ...chapter,
          syncedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        if (existingChapter.data.length > 0) {
          // 更新现有章节
          await db.collection('novelChapters').doc(existingChapter.data[0]._id).update(chapterData);
          updatedCount++;
        } else {
          // 创建新章节
          await db.collection('novelChapters').add(chapterData);
          addedCount++;
        }
      }

      results.novelChapters = { action: 'synced', added: addedCount, updated: updatedCount };
    }

    console.log('数据同步上传完成:', userId, results);
    return results;

  } catch (error) {
    console.error('数据同步上传失败:', error);
    throw error;
  }
}

// 从CloudBase下载用户数据
async function downloadUserData(userId, dataTypes = ['userSettings', 'userFavorites', 'userHistory', 'novels', 'shortStories', 'novelChapters']) {
  const syncData = {};

  try {
    // 下载用户设置
    if (dataTypes.includes('userSettings')) {
      const settingsResult = await db.collection('userSettings').where({
        userId: userId
      }).get();

      if (settingsResult.data.length > 0) {
        syncData.userSettings = settingsResult.data[0].settings;
      } else {
        // 返回默认设置
        syncData.userSettings = {
          enableBiometric: false,
          autoSync: true,
          enableNotification: true,
          theme: 'system',
          language: 'zh-CN'
        };
      }
    }

    // 下载用户收藏
    if (dataTypes.includes('userFavorites')) {
      const favoritesResult = await db.collection('userFavorites').where({
        userId: userId
      }).get();

      syncData.userFavorites = favoritesResult.data.map(item => {
        const { _id, userId, syncedAt, ...favoriteData } = item;
        return favoriteData;
      });
    }

    // 下载用户历史记录
    if (dataTypes.includes('userHistory')) {
      const historyResult = await db.collection('userHistory').where({
        userId: userId
      }).get();

      syncData.userHistory = historyResult.data.map(item => {
        const { _id, userId, syncedAt, ...historyData } = item;
        return historyData;
      });
    }

    // 下载长篇小说
    if (dataTypes.includes('novels')) {
      try {
        const novelsResult = await db.collection('novels').where({
          userId: userId
        }).get();

        syncData.novels = novelsResult.data.map(item => {
          const { _id, userId, syncedAt, ...novelData } = item;
          return novelData;
        });
      } catch (error) {
        console.error('下载长篇小说失败:', error);
        syncData.novels = []; // 返回空数组
      }
    }

    // 下载短篇小说
    if (dataTypes.includes('shortStories')) {
      try {
        const storiesResult = await db.collection('shortStories').where({
          userId: userId
        }).get();

        syncData.shortStories = storiesResult.data.map(item => {
          const { _id, userId, syncedAt, ...storyData } = item;
          return storyData;
        });
      } catch (error) {
        console.error('下载短篇小说失败:', error);
        syncData.shortStories = []; // 返回空数组
      }
    }

    // 下载小说章节
    if (dataTypes.includes('novelChapters')) {
      try {
        const chaptersResult = await db.collection('novelChapters').where({
          userId: userId
        }).get();

        syncData.novelChapters = chaptersResult.data.map(item => {
          const { _id, userId, syncedAt, ...chapterData } = item;
          return chapterData;
        });
      } catch (error) {
        console.error('下载小说章节失败:', error);
        syncData.novelChapters = []; // 返回空数组
      }
    }

    console.log('数据同步下载完成:', userId, Object.keys(syncData));
    return syncData;

  } catch (error) {
    console.error('数据同步下载失败:', error);
    throw error;
  }
}

// 获取用户同步状态
async function getUserSyncStatus(userId) {
  try {
    const status = {
      userId: userId,
      lastSyncTime: null,
      dataTypes: {},
      isEnabled: true
    };

    // 检查各种数据类型的同步状态
    const collections = ['userSettings', 'userFavorites', 'userHistory', 'novels', 'shortStories', 'novelChapters'];

    for (const collection of collections) {
      const result = await db.collection(collection).where({
        userId: userId
      }).get();

      status.dataTypes[collection] = {
        count: result.data.length,
        lastUpdated: result.data.length > 0 ?
          Math.max(...result.data.map(item => new Date(item.updatedAt || item.syncedAt || 0).getTime())) : null
      };

      if (status.dataTypes[collection].lastUpdated) {
        status.dataTypes[collection].lastUpdated = new Date(status.dataTypes[collection].lastUpdated).toISOString();

        if (!status.lastSyncTime || status.dataTypes[collection].lastUpdated > status.lastSyncTime) {
          status.lastSyncTime = status.dataTypes[collection].lastUpdated;
        }
      }
    }

    return status;

  } catch (error) {
    console.error('获取同步状态失败:', error);
    throw error;
  }
}

// CloudBase API入口
exports.main = async (event, context) => {
  console.log('=== CloudBase Function Called ===');
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  try {
    // 处理OPTIONS请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: ''
      };
    }

    // 简单的路由处理
    const path = event.path || '/';
    const method = event.httpMethod || 'GET';

    console.log(`Processing ${method} ${path}`);

    // 基本的API响应
    if (path === '/api' || path === '/') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: 'Novel App API is running on CloudBase',
          timestamp: new Date().toISOString(),
          path: path,
          method: method
        })
      };
    }

    // 登录接口
    if ((path === '/api/auth/login' || path === '/auth/login') && method === 'POST') {
      let body;
      try {
        body = event.body ? (typeof event.body === 'string' ? JSON.parse(event.body) : event.body) : {};
      } catch (e) {
        console.log('Failed to parse request body:', e);
        console.log('Raw body:', event.body);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '请求数据格式错误'
          })
        };
      }

      console.log('Login request body:', body);
      console.log('Username:', body.username);
      console.log('Password hash:', body.password);
      console.log('Request path:', path);
      console.log('Request method:', method);

      // 从CloudBase数据库查找用户
      let user = null;
      try {
        const userResult = await db.collection('users').where({
          username: body.username
        }).get();

        if (userResult.data.length > 0) {
          user = userResult.data[0];
          console.log('从CloudBase数据库找到用户:', user.username);
        } else {
          console.log('CloudBase数据库中未找到用户，尝试本地文件');
          // 如果CloudBase数据库中没有找到，尝试本地文件
          const database = loadDatabase();
          user = database.users.find(u => u.username === body.username);
          if (user) {
            console.log('从本地文件找到用户:', user.username);
          }
        }
      } catch (dbError) {
        console.log('CloudBase数据库查询失败，使用本地文件:', dbError);
        // 如果数据库查询失败，使用本地文件作为备用
        const database = loadDatabase();
        user = database.users.find(u => u.username === body.username);
        if (user) {
          console.log('从本地文件找到用户:', user.username);
        }
      }

      if (!user) {
        console.log('User not found:', body.username);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        };
      }

      // 验证密码
      console.log('Stored password hash:', user.passwordHash);
      console.log('Received password hash:', body.password);

      // 如果数据库中没有密码哈希，使用默认的test123哈希
      const expectedHash = user.passwordHash || 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae';

      const userInfo = { passwordHash: expectedHash, user: user };
      if (userInfo && body.password === userInfo.passwordHash) {
        console.log('Login successful for user:', body.username);
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              token: `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              refreshToken: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              user: {
                id: user.id || '',
                username: user.username || '',
                phoneNumber: user.phoneNumber || '',
                email: user.email || null,
                avatar: user.avatar || null,
                isMember: user.isMember || false,
                memberExpireTime: user.memberExpireTime || null,
                membershipType: _normalizeMembershipType(user.membershipType),
                isPermanentMember: user.isPermanentMember || false,
                isDataSyncEnabled: user.isDataSyncEnabled !== false,
                memberCode: user.memberCode || null,
                settings: user.settings || {
                  enableBiometric: false,
                  autoSync: true,
                  enableNotification: true,
                  theme: 'system',
                  language: 'zh-CN'
                },
                createdAt: user.createdAt || new Date().toISOString(),
                updatedAt: user.updatedAt || new Date().toISOString()
              },
              expiresIn: 86400
            }
          })
        };
      } else {
        console.log('Login failed - invalid credentials');
        console.log('Expected hash for testuser:', validUsers.testuser?.passwordHash);
        console.log('Received hash:', body.password);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        };
      }
    }

    // 获取套餐接口
    if ((path === '/api/packages' || path === '/packages') && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'monthly',
              name: '月度会员',
              description: '享受一个月的会员特权，包含所有高级功能',
              price: 18.88,
              durationDays: 30,
              features: [
                '无限制章节生成',
                '高级AI模型访问',
                '多格式导出',
                '扩展功能使用',
                '优先客服支持'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: 100,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 1,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            },
            {
              id: 'permanent',
              name: '永久会员',
              description: '一次购买，终身享受所有会员特权',
              price: 188.88,
              durationDays: -1,
              features: [
                '永久无限制章节生成',
                '永久高级AI模型访问',
                '永久多格式导出',
                '永久扩展功能使用',
                '永久优先客服支持',
                '未来新功能免费使用'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: -1,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 2,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            }
          ]
        })
      };
    }

    // 发送验证码接口
    if ((path === '/api/auth/send-code' || path === '/auth/send-code') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const phoneNumber = body.phoneNumber;

      console.log('发送验证码请求:', phoneNumber);

      if (!phoneNumber) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '手机号不能为空'
          })
        };
      }

      // 生成6位验证码
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      console.log(`为手机号 ${phoneNumber} 生成验证码: ${verificationCode}`);

      // TODO: 在这里集成真实的短信服务
      // 目前是模拟实现，实际部署时需要替换为真实的短信发送逻辑

      try {
        // 模拟短信发送（开发阶段）
        const smsResult = await sendSMS(phoneNumber, verificationCode);

        if (smsResult.success) {
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '验证码发送成功',
              // 开发阶段返回验证码，生产环境应该删除这行
              debugCode: verificationCode
            })
          };
        } else {
          return {
            statusCode: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '验证码发送失败，请稍后重试'
            })
          };
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证码发送失败，请稍后重试'
          })
        };
      }
    }

    // 用户注册接口
    if ((path === '/api/auth/register' || path === '/auth/register') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { username, password, phoneNumber, verificationCode, memberCode } = body;

      console.log('用户注册请求:', { username, phoneNumber, memberCode });

      // 验证必填字段
      if (!username || !password || !phoneNumber) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名、密码和手机号不能为空'
          })
        };
      }

      try {
        // 检查用户名和手机号是否已存在（使用CloudBase数据库）
        try {
          const existingUserByUsername = await db.collection('users').where({
            username: username
          }).get();

          if (existingUserByUsername.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '用户名已存在'
              })
            };
          }

          const existingUserByPhone = await db.collection('users').where({
            phoneNumber: phoneNumber
          }).get();

          if (existingUserByPhone.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '手机号已被注册'
              })
            };
          }
        } catch (dbError) {
          console.log('数据库查询失败，使用本地文件备用:', dbError);
          // 如果数据库查询失败，使用本地文件作为备用
          const database = loadDatabase();

          const existingUserByUsername = database.users.find(u => u.username === username);
          if (existingUserByUsername) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '用户名已存在'
              })
            };
          }

          const existingUserByPhone = database.users.find(u => u.phoneNumber === phoneNumber);
          if (existingUserByPhone) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '手机号已被注册'
              })
            };
          }
        }

        // 验证会员码（如果提供）
        let memberInfo = null;
        if (memberCode) {
          try {
            const memberCodeResult = await db.collection('memberData').where({
              code: memberCode,
              isUsed: false
            }).get();

            if (memberCodeResult.data.length === 0) {
              return {
                statusCode: 400,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '会员码无效或已被使用'
                })
              };
            }
            memberInfo = memberCodeResult.data[0];
          } catch (dbError) {
            console.log('会员码查询失败，使用本地文件备用:', dbError);
            // 如果数据库查询失败，使用本地文件作为备用
            const database = loadDatabase();
            const memberCodeData = database.memberCodes || [];
            memberInfo = memberCodeData.find(mc => mc.code === memberCode && !mc.isUsed);

            if (!memberInfo) {
              return {
                statusCode: 400,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '会员码无效或已被使用'
                })
              };
            }
          }
        }

        // 创建新用户
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newUser = {
          id: userId,
          username,
          phoneNumber,
          email: null,
          avatar: null,
          passwordHash: password, // Flutter端已经哈希过了
          isMember: !!memberInfo,
          memberExpireTime: memberInfo ? null : null,
          membershipType: memberInfo ? 'permanent' : 'none',
          isPermanentMember: !!memberInfo,
          memberCode: memberCode || null,
          isDataSyncEnabled: true,
          settings: {
            enableBiometric: false,
            autoSync: true,
            enableNotification: true,
            theme: 'system',
            language: 'zh-CN'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 保存用户到CloudBase数据库
        try {
          await db.collection('users').add(newUser);
          console.log('=== 用户成功保存到CloudBase数据库 ===');
          console.log('用户ID:', userId);
          console.log('用户名:', username);
          console.log('手机号:', phoneNumber);
          console.log('会员状态:', !!memberInfo);

          // 如果使用了会员码，标记为已使用
          if (memberInfo) {
            await db.collection('memberData').doc(memberInfo._id).update({
              isUsed: true,
              usedBy: userId,
              usedAt: new Date().toISOString()
            });
            console.log('会员码已标记为使用:', memberCode);
          }

        } catch (dbError) {
          console.error('保存用户到数据库失败:', dbError);
          console.log('=== 用户注册信息（数据库保存失败，仅记录日志） ===');
          console.log('用户ID:', userId);
          console.log('用户名:', username);
          console.log('手机号:', phoneNumber);
          console.log('会员状态:', !!memberInfo);
          console.log('会员码:', memberCode || '无');
          console.log('注册时间:', new Date().toISOString());
          console.log('用户数据:', JSON.stringify(newUser, null, 2));

          // 即使数据库保存失败，也继续返回成功响应（用户体验优先）
        }

        // 生成Token
        const token = `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const refreshToken = `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              token,
              refreshToken,
              user: {
                id: newUser.id,
                username: newUser.username,
                phoneNumber: newUser.phoneNumber,
                email: newUser.email,
                avatar: newUser.avatar,
                isMember: newUser.isMember,
                memberExpireTime: newUser.memberExpireTime,
                membershipType: _normalizeMembershipType(newUser.membershipType),
                isPermanentMember: newUser.isPermanentMember,
                isDataSyncEnabled: newUser.isDataSyncEnabled,
                memberCode: newUser.memberCode,
                settings: newUser.settings,
                createdAt: newUser.createdAt,
                updatedAt: newUser.updatedAt
              },
              expiresIn: 86400
            }
          })
        };

      } catch (error) {
        console.error('用户注册失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '注册失败，请稍后重试'
          })
        };
      }
    }

    // 验证验证码接口
    if ((path === '/api/auth/verify-code' || path === '/auth/verify-code') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.code === '123456') {
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '验证码验证成功'
          })
        };
      } else {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证码错误'
          })
        };
      }
    }

    // 验证会员码接口
    if ((path === '/api/member-code/validate' || path === '/member-code/validate') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};

      try {
        const memberCodeResult = await db.collection('memberData').where({
          code: body.code,
          isUsed: false
        }).get();

        if (memberCodeResult.data.length > 0) {
          const memberCode = memberCodeResult.data[0];
          // 检查是否过期
          if (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date()) {
            return {
              statusCode: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: true,
                message: '会员码有效'
              })
            };
          }
        }

        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码无效或已过期'
          })
        };
      } catch (error) {
        console.error('验证会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证会员码失败'
          })
        };
      }
    }

    // 数据同步下载接口
    if ((path === '/api/sync/download' || path === '/sync/download') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { userId, token, dataTypes } = body;

      console.log('数据同步下载请求:', { userId, dataTypes });

      if (!userId || !token) {
        return {
          statusCode: 401,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户认证信息缺失'
          })
        };
      }

      try {
        const syncData = await downloadUserData(userId, dataTypes);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '数据获取成功',
            data: {
              syncData,
              syncedAt: new Date().toISOString(),
              availableTypes: Object.keys(syncData)
            }
          })
        };

      } catch (error) {
        console.error('数据同步下载失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据获取失败，请稍后重试'
          })
        };
      }
    }

    // 数据同步上传接口
    if ((path === '/api/sync/upload' || path === '/sync/upload') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { userId, token, syncData } = body;

      console.log('数据同步上传请求:', { userId, dataTypes: Object.keys(syncData || {}) });

      if (!userId || !token) {
        return {
          statusCode: 401,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户认证信息缺失'
          })
        };
      }

      if (!syncData) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '同步数据不能为空'
          })
        };
      }

      try {
        const syncResult = await uploadUserData(userId, syncData);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '数据同步成功',
            data: {
              syncedAt: new Date().toISOString(),
              syncedTypes: Object.keys(syncData),
              results: syncResult
            }
          })
        };

      } catch (error) {
        console.error('数据同步上传失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据同步失败，请稍后重试'
          })
        };
      }
    }

    // 数据同步状态查询接口
    if ((path === '/api/sync/status' || path === '/sync/status') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { userId, token } = body;

      if (!userId || !token) {
        return {
          statusCode: 401,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户认证信息缺失'
          })
        };
      }

      try {
        const syncStatus = await getUserSyncStatus(userId);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: syncStatus
          })
        };

      } catch (error) {
        console.error('获取同步状态失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取同步状态失败'
          })
        };
      }
    }

    // 生成会员码工具函数
    function generateMemberCode(prefix = 'VIP', length = 8) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = prefix;
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    }

    // 创建单个会员码接口
    if ((path === '/api/admin/member-code/create' || path === '/admin/member-code/create') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { packageId, expireAt, batchId, customCode, prefix } = body;

      try {
        // 生成或使用自定义会员码
        let code = customCode;
        if (!code) {
          let attempts = 0;
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              return {
                statusCode: 500,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '生成唯一会员码失败，请重试'
                })
              };
            }

            // 检查会员码是否已存在
            const existingCode = await db.collection('memberData').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);
        } else {
          // 检查自定义会员码是否已存在
          const existingCode = await db.collection('memberData').where({ code }).get();
          if (existingCode.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '会员码已存在'
              })
            };
          }
        }

        const newMemberCode = {
          code,
          packageId: packageId || 'pkg_permanent',
          isUsed: false,
          usedBy: null,
          usedAt: null,
          expireAt: expireAt || null,
          batchId: batchId || `batch_${Date.now()}`,
          createdAt: new Date().toISOString()
        };

        const result = await db.collection('memberData').add(newMemberCode);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: { ...newMemberCode, _id: result.id },
            message: '会员码创建成功'
          })
        };
      } catch (error) {
        console.error('创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '创建会员码失败'
          })
        };
      }
    }

    // 批量创建会员码接口
    if ((path === '/api/admin/member-code/batch-create' || path === '/admin/member-code/batch-create') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { packageId, count, expireAt, batchId, prefix } = body;

      try {
        const createdCodes = [];
        const currentBatchId = batchId || `batch_${Date.now()}`;
        const codeCount = Math.min(count || 10, 100); // 限制最多100个

        for (let i = 0; i < codeCount; i++) {
          let code;
          let attempts = 0;

          // 生成唯一的会员码
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              break;
            }

            const existingCode = await db.collection('memberData').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);

          if (attempts <= 100) {
            const newMemberCode = {
              code,
              packageId: packageId || 'pkg_permanent',
              isUsed: false,
              usedBy: null,
              usedAt: null,
              expireAt: expireAt || null,
              batchId: currentBatchId,
              createdAt: new Date().toISOString()
            };

            const result = await db.collection('memberData').add(newMemberCode);
            createdCodes.push({ ...newMemberCode, _id: result.id });
          }
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              batchId: currentBatchId,
              count: createdCodes.length,
              codes: createdCodes
            },
            message: `成功创建 ${createdCodes.length} 个会员码`
          })
        };
      } catch (error) {
        console.error('批量创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '批量创建会员码失败'
          })
        };
      }
    }

    // 获取会员码列表接口
    if ((path === '/api/admin/member-code/list' || path === '/admin/member-code/list') && method === 'GET') {
      const query = event.queryStringParameters || {};
      const { page = 1, limit = 20, isUsed, packageId, batchId } = query;

      try {
        let queryConditions = {};

        // 构建查询条件
        if (isUsed !== undefined) {
          queryConditions.isUsed = isUsed === 'true';
        }

        if (packageId) {
          queryConditions.packageId = packageId;
        }

        if (batchId) {
          queryConditions.batchId = batchId;
        }

        // 查询会员码
        const memberCodesResult = await db.collection('memberData')
          .where(queryConditions)
          .orderBy('createdAt', 'desc')
          .skip((parseInt(page) - 1) * parseInt(limit))
          .limit(parseInt(limit))
          .get();

        // 获取总数
        const totalResult = await db.collection('memberData').where(queryConditions).count();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              codes: memberCodesResult.data,
              pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalResult.total,
                totalPages: Math.ceil(totalResult.total / parseInt(limit))
              }
            }
          })
        };
      } catch (error) {
        console.error('获取会员码列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码列表失败'
          })
        };
      }
    }

    // 获取会员码统计信息接口
    if ((path === '/api/admin/member-code/stats' || path === '/admin/member-code/stats') && method === 'GET') {
      try {
        const allCodesResult = await db.collection('memberData').get();
        const memberCodes = allCodesResult.data;

        const stats = {
          total: memberCodes.length,
          used: memberCodes.filter(code => code.isUsed).length,
          unused: memberCodes.filter(code => !code.isUsed).length,
          expired: memberCodes.filter(code =>
            code.expireAt && new Date(code.expireAt) < new Date()
          ).length,
          byPackage: {},
          byBatch: {}
        };

        // 按套餐统计
        memberCodes.forEach(code => {
          if (!stats.byPackage[code.packageId]) {
            stats.byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byPackage[code.packageId].total++;
          if (code.isUsed) {
            stats.byPackage[code.packageId].used++;
          } else {
            stats.byPackage[code.packageId].unused++;
          }
        });

        // 按批次统计
        memberCodes.forEach(code => {
          if (!stats.byBatch[code.batchId]) {
            stats.byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byBatch[code.batchId].total++;
          if (code.isUsed) {
            stats.byBatch[code.batchId].used++;
          } else {
            stats.byBatch[code.batchId].unused++;
          }
        });

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: stats
          })
        };
      } catch (error) {
        console.error('获取会员码统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码统计失败'
          })
        };
      }
    }

    // 删除会员码接口
    if ((path.startsWith('/api/admin/member-code/') || path.startsWith('/admin/member-code/')) && method === 'DELETE') {
      const codeToDelete = path.split('/').pop();

      try {
        const memberCodeResult = await db.collection('memberData').where({ code: codeToDelete }).get();

        if (memberCodeResult.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '会员码不存在'
            })
          };
        }

        const memberCode = memberCodeResult.data[0];
        if (memberCode.isUsed) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '已使用的会员码不能删除'
            })
          };
        }

        await db.collection('memberData').doc(memberCode._id).remove();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码删除成功'
          })
        };
      } catch (error) {
        console.error('删除会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '删除会员码失败'
          })
        };
      }
    }

    // 默认404响应
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        message: 'API endpoint not found',
        path: path,
        method: method
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
