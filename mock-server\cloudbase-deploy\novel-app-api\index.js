const fs = require('fs');
const path = require('path');
const tcb = require('@cloudbase/node-sdk');

// 初始化CloudBase SDK
const app = tcb.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 加载本地数据库作为备用
function loadDatabase() {
  try {
    const dbPath = path.join(__dirname, 'db.json');
    const data = fs.readFileSync(dbPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log('加载本地数据库失败:', error);
    return { users: [], packages: [], memberCodes: [] };
  }
}

// 生成会员码工具函数
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 云函数主入口
exports.main = async (event, context) => {
  try {
    const method = event.httpMethod;
    const path = event.path;
    const body = event.body ? JSON.parse(event.body) : {};
    const query = event.queryStringParameters || {};

    console.log(`请求: ${method} ${path}`);

    // 处理OPTIONS请求
    if (method === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: ''
      };
    }

    // 发送验证码接口
    if ((path === '/api/auth/send-code' || path === '/auth/send-code') && method === 'POST') {
      const { phoneNumber } = body;

      try {
        // 开发环境使用固定验证码，生产环境生成随机验证码
        const code = '123456'; // 固定验证码，便于测试

        // TODO: 这里应该调用真实的短信服务发送验证码
        // 目前只是模拟发送
        console.log(`发送验证码到 ${phoneNumber}: ${code}`);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '验证码发送成功',
            // 开发环境下返回验证码
            debugCode: code
          })
        };
      } catch (error) {
        console.error('发送验证码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '发送验证码失败'
          })
        };
      }
    }

    // 验证验证码接口
    if ((path === '/api/auth/verify-code' || path === '/auth/verify-code') && method === 'POST') {
      const { phoneNumber, code } = body;

      try {
        // 开发环境使用固定验证码验证
        if (code === '123456') {
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '验证码验证成功'
            })
          };
        } else {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '验证码错误'
            })
          };
        }
      } catch (error) {
        console.error('验证验证码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证验证码失败'
          })
        };
      }
    }

    // 验证会员码接口
    if ((path === '/api/member-code/validate' || path === '/member-code/validate') && method === 'POST') {
      try {
        const memberCodeResult = await db.collection('memberData').where({
          code: body.code,
          isUsed: false
        }).get();

        if (memberCodeResult.data.length > 0) {
          const memberCode = memberCodeResult.data[0];
          // 检查是否过期
          if (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date()) {
            return {
              statusCode: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: true,
                message: '会员码有效'
              })
            };
          }
        }

        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码无效或已过期'
          })
        };
      } catch (error) {
        console.error('验证会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证会员码失败'
          })
        };
      }
    }

    // 用户注册接口
    if ((path === '/api/auth/register' || path === '/auth/register') && method === 'POST') {
      const { username, phoneNumber, password, nickname, verificationCode, memberCode } = body;

      try {
        // 检查用户是否已存在（检查用户名和手机号）
        const existingUserByPhone = await db.collection('users').where({ phoneNumber }).get();
        const existingUserByUsername = await db.collection('users').where({ username }).get();

        if (existingUserByPhone.data.length > 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '手机号已注册'
            })
          };
        }

        if (existingUserByUsername.data.length > 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户名已存在'
            })
          };
        }

        // 生成用户ID
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 创建用户基本信息
        const newUser = {
          id: userId,
          username: username || `user${phoneNumber.substr(-4)}`,
          phoneNumber,
          email: null,
          avatar: null,
          passwordHash: password, // 生产环境应该加密
          isMember: false,
          memberExpireTime: null,
          membershipType: 'none',
          isPermanentMember: false,
          memberCode: null,
          isDataSyncEnabled: true,
          settings: {
            enableBiometric: false,
            autoSync: true,
            enableNotification: true,
            theme: 'system',
            language: 'zh-CN'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 如果提供了会员码，验证并激活会员
        if (memberCode) {
          const memberCodeResult = await db.collection('memberData').where({
            code: memberCode,
            isUsed: false
          }).get();

          if (memberCodeResult.data.length > 0) {
            const memberCodeData = memberCodeResult.data[0];

            // 检查会员码是否过期
            if (!memberCodeData.expireAt || new Date(memberCodeData.expireAt) > new Date()) {
              // 激活会员
              if (memberCodeData.packageId === 'pkg_permanent') {
                newUser.membershipType = 'permanent';
                newUser.memberExpireTime = null;
                newUser.isPermanentMember = true;
                newUser.isMember = true;
              } else if (memberCodeData.packageId === 'pkg_monthly') {
                newUser.membershipType = 'monthly';
                newUser.memberExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
                newUser.isPermanentMember = false;
                newUser.isMember = true;
              }

              newUser.memberCode = memberCode;

              // 标记会员码为已使用
              await db.collection('memberData').doc(memberCodeData._id).update({
                isUsed: true,
                usedBy: userId,
                usedAt: new Date().toISOString()
              });
            }
          }
        }

        // 保存用户到数据库
        const userResult = await db.collection('users').add(newUser);

        // 生成JWT Token（这里简化处理，生产环境需要更安全的实现）
        const token = Buffer.from(JSON.stringify({ userId, exp: Date.now() + 24 * 60 * 60 * 1000 })).toString('base64');

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '注册成功',
            data: {
              token,
              refreshToken: token, // 简化处理，使用相同token
              user: newUser,
              expiresIn: 86400
            }
          })
        };
      } catch (error) {
        console.error('用户注册失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '注册失败'
          })
        };
      }
    }

    // 用户登录接口
    if ((path === '/api/auth/login' || path === '/auth/login') && method === 'POST') {
      const { username, password } = body;

      try {
        // 查找用户（支持用户名登录）
        const userResult = await db.collection('users').where({ username }).get();

        if (userResult.data.length === 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户名或密码错误'
            })
          };
        }

        const user = userResult.data[0];

        // 验证密码（生产环境应该使用加密比较）
        if (user.passwordHash !== password) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户名或密码错误'
            })
          };
        }

        // 生成JWT Token
        const token = Buffer.from(JSON.stringify({ userId: user.id, exp: Date.now() + 24 * 60 * 60 * 1000 })).toString('base64');

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '登录成功',
            data: {
              token,
              refreshToken: token, // 简化处理，使用相同token
              user: user,
              expiresIn: 86400
            }
          })
        };
      } catch (error) {
        console.error('用户登录失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '登录失败'
          })
        };
      }
    }

    // 创建单个会员码接口
    if ((path === '/api/admin/member-code/create' || path === '/admin/member-code/create') && method === 'POST') {
      const { packageId, expireAt, batchId, customCode, prefix } = body;

      try {
        // 生成或使用自定义会员码
        let code = customCode;
        if (!code) {
          let attempts = 0;
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              return {
                statusCode: 500,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '生成唯一会员码失败，请重试'
                })
              };
            }

            // 检查会员码是否已存在
            const existingCode = await db.collection('memberData').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);
        } else {
          // 检查自定义会员码是否已存在
          const existingCode = await db.collection('memberData').where({ code }).get();
          if (existingCode.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '会员码已存在'
              })
            };
          }
        }

        const newMemberCode = {
          code,
          packageId: packageId || 'pkg_permanent',
          isUsed: false,
          usedBy: null,
          usedAt: null,
          expireAt: expireAt || null,
          batchId: batchId || `batch_${Date.now()}`,
          createdAt: new Date().toISOString()
        };

        const result = await db.collection('memberData').add(newMemberCode);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: { ...newMemberCode, _id: result.id },
            message: '会员码创建成功'
          })
        };
      } catch (error) {
        console.error('创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '创建会员码失败'
          })
        };
      }
    }

    // 批量创建会员码接口
    if ((path === '/api/admin/member-code/batch-create' || path === '/admin/member-code/batch-create') && method === 'POST') {
      const { packageId, count, expireAt, batchId, prefix } = body;

      try {
        const createdCodes = [];
        const currentBatchId = batchId || `batch_${Date.now()}`;
        const codeCount = Math.min(count || 10, 100); // 限制最多100个

        for (let i = 0; i < codeCount; i++) {
          let code;
          let attempts = 0;

          // 生成唯一的会员码
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              break;
            }

            const existingCode = await db.collection('memberData').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);

          if (attempts <= 100) {
            const newMemberCode = {
              code,
              packageId: packageId || 'pkg_permanent',
              isUsed: false,
              usedBy: null,
              usedAt: null,
              expireAt: expireAt || null,
              batchId: currentBatchId,
              createdAt: new Date().toISOString()
            };

            const result = await db.collection('memberData').add(newMemberCode);
            createdCodes.push({ ...newMemberCode, _id: result.id });
          }
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              batchId: currentBatchId,
              count: createdCodes.length,
              codes: createdCodes
            },
            message: `成功创建 ${createdCodes.length} 个会员码`
          })
        };
      } catch (error) {
        console.error('批量创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '批量创建会员码失败'
          })
        };
      }
    }

    // 获取会员码列表接口
    if ((path === '/api/admin/member-code/list' || path === '/admin/member-code/list') && method === 'GET') {
      const { page = 1, limit = 20, isUsed, packageId, batchId } = query;

      try {
        let queryConditions = {};

        // 构建查询条件
        if (isUsed !== undefined) {
          queryConditions.isUsed = isUsed === 'true';
        }

        if (packageId) {
          queryConditions.packageId = packageId;
        }

        if (batchId) {
          queryConditions.batchId = batchId;
        }

        // 查询会员码
        const memberCodesResult = await db.collection('memberData')
          .where(queryConditions)
          .orderBy('createdAt', 'desc')
          .skip((parseInt(page) - 1) * parseInt(limit))
          .limit(parseInt(limit))
          .get();

        // 获取总数
        const totalResult = await db.collection('memberData').where(queryConditions).count();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              codes: memberCodesResult.data,
              pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalResult.total,
                totalPages: Math.ceil(totalResult.total / parseInt(limit))
              }
            }
          })
        };
      } catch (error) {
        console.error('获取会员码列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码列表失败'
          })
        };
      }
    }

    // 获取会员码统计信息接口
    if ((path === '/api/admin/member-code/stats' || path === '/admin/member-code/stats') && method === 'GET') {
      try {
        const allCodesResult = await db.collection('memberData').get();
        const memberCodes = allCodesResult.data;

        const stats = {
          total: memberCodes.length,
          used: memberCodes.filter(code => code.isUsed).length,
          unused: memberCodes.filter(code => !code.isUsed).length,
          expired: memberCodes.filter(code =>
            code.expireAt && new Date(code.expireAt) < new Date()
          ).length,
          byPackage: {},
          byBatch: {}
        };

        // 按套餐统计
        memberCodes.forEach(code => {
          if (!stats.byPackage[code.packageId]) {
            stats.byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byPackage[code.packageId].total++;
          if (code.isUsed) {
            stats.byPackage[code.packageId].used++;
          } else {
            stats.byPackage[code.packageId].unused++;
          }
        });

        // 按批次统计
        memberCodes.forEach(code => {
          if (!stats.byBatch[code.batchId]) {
            stats.byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byBatch[code.batchId].total++;
          if (code.isUsed) {
            stats.byBatch[code.batchId].used++;
          } else {
            stats.byBatch[code.batchId].unused++;
          }
        });

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: stats
          })
        };
      } catch (error) {
        console.error('获取会员码统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码统计失败'
          })
        };
      }
    }

    // 删除会员码接口
    if ((path.startsWith('/api/admin/member-code/') || path.startsWith('/admin/member-code/')) && method === 'DELETE') {
      const codeToDelete = path.split('/').pop();

      try {
        const memberCodeResult = await db.collection('memberData').where({ code: codeToDelete }).get();

        if (memberCodeResult.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '会员码不存在'
            })
          };
        }

        const memberCode = memberCodeResult.data[0];
        if (memberCode.isUsed) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '已使用的会员码不能删除'
            })
          };
        }

        await db.collection('memberData').doc(memberCode._id).remove();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码删除成功'
          })
        };
      } catch (error) {
        console.error('删除会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '删除会员码失败'
          })
        };
      }
    }

    // 数据同步上传（分批）
    if (path === '/sync/upload' && method === 'POST') {
      try {
        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证Token'
            })
          };
        }

        const token = authHeader.substring(7);

        // 解析Token获取用户ID（简化处理）
        let userId;
        try {
          const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
          userId = tokenData.userId;
        } catch (e) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证Token'
            })
          };
        }

        // 验证用户是否存在且为会员
        const user = await db.collection('users').where({
          id: userId
        }).get();

        if (user.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }

        const userData = user.data[0];
        if (!userData.isMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '数据同步功能仅限会员使用'
            })
          };
        }

        // 验证请求数据
        if (!body || typeof body !== 'object') {
          throw new Error('请求体为空或格式不正确');
        }

        // 支持分批上传
        const batchInfo = body.batchInfo || { isComplete: true, batchId: null, batchIndex: 0, totalBatches: 1 };
        const dataType = body.dataType || 'full'; // full, novels, characterCards, knowledgeDocuments, userSettings

        console.log(`同步数据类型: ${dataType}, 批次: ${batchInfo.batchIndex + 1}/${batchInfo.totalBatches}`);
        console.log('请求数据大小:', JSON.stringify(body).length, '字节');

        // 验证数据类型
        const validDataTypes = ['full', 'novels', 'characterCards', 'knowledgeDocuments', 'userSettings', 'characterTypes', 'stylePackages'];
        if (!validDataTypes.includes(dataType)) {
          throw new Error(`无效的数据类型: ${dataType}`);
        }

        // 获取现有同步数据
        let existingSyncData = userData.syncData || {};

        // 根据数据类型更新对应部分
        if (dataType === 'full') {
          existingSyncData = body.data || {};
        } else {
          // 分类型更新
          if (body.data && body.data[dataType]) {
            if (batchInfo.batchIndex === 0) {
              // 第一批，重置该类型数据
              existingSyncData[dataType] = body.data[dataType];
            } else {
              // 后续批次，合并数据
              if (!existingSyncData[dataType]) {
                existingSyncData[dataType] = [];
              }
              if (Array.isArray(existingSyncData[dataType]) && Array.isArray(body.data[dataType])) {
                existingSyncData[dataType] = existingSyncData[dataType].concat(body.data[dataType]);
              } else {
                existingSyncData[dataType] = body.data[dataType];
              }
            }
          }
        }

        // 更新用户的同步数据（简化版本，不使用批次信息字段）
        const updateData = {
          syncData: existingSyncData,
          syncTimestamp: body.timestamp || new Date().toISOString(),
          syncUpdatedAt: new Date().toISOString()
        };

        // 暂时不保存批次信息到数据库，避免字段冲突问题
        // 批次信息只在内存中处理，不持久化存储

        // 更新用户记录
        try {
          console.log('准备更新用户数据，用户ID:', userId);
          console.log('更新数据大小:', JSON.stringify(updateData).length, '字节');

          // 检查数据大小，如果太大则只保存关键信息
          const updateDataString = JSON.stringify(updateData);
          if (updateDataString.length > 100000) { // 如果超过100KB
            console.log('数据过大，进行精简处理...');
            updateData.syncData = {
              novels: (existingSyncData.novels || []).slice(0, 10), // 只保存前10本小说
              knowledgeDocuments: (existingSyncData.knowledgeDocuments || []).slice(0, 5), // 只保存前5个文档
              characterCards: existingSyncData.characterCards || [],
              userSettings: existingSyncData.userSettings || {}
            };
          }

          await db.collection('users').doc(userData._id).update(updateData);
          console.log('用户数据更新成功');
        } catch (dbError) {
          console.error('数据库更新失败:', dbError);
          throw new Error(`数据库更新失败: ${dbError.message}`);
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: batchInfo.isComplete ? '数据同步上传成功' : `批次 ${batchInfo.batchIndex + 1}/${batchInfo.totalBatches} 上传成功`,
            timestamp: updateData.syncTimestamp,
            batchInfo: batchInfo
          })
        };
      } catch (error) {
        console.error('数据同步上传失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据同步上传失败',
            error: error.message
          })
        };
      }
    }

    // 数据同步下载
    if (path === '/sync/download' && method === 'GET') {
      try {
        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证Token'
            })
          };
        }

        const token = authHeader.substring(7);

        // 解析Token获取用户ID
        let userId;
        try {
          const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
          userId = tokenData.userId;
        } catch (e) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证Token'
            })
          };
        }

        // 验证用户是否存在且为会员
        const user = await db.collection('users').where({
          id: userId
        }).get();

        if (user.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }

        const userData = user.data[0];
        if (!userData.isMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '数据同步功能仅限会员使用'
            })
          };
        }

        // 从用户记录中获取同步数据
        const data = userData.syncData || {};
        const timestamp = userData.syncTimestamp || null;

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '数据同步下载成功',
            data: data,
            timestamp: timestamp
          })
        };
      } catch (error) {
        console.error('数据同步下载失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据同步下载失败',
            error: error.message
          })
        };
      }
    }

    // 获取会员套餐
    if (path === '/packages' && method === 'GET') {
      try {
        // 从本地数据库获取套餐信息
        const localDb = loadDatabase();
        const packages = localDb.packages ? localDb.packages.filter(pkg => pkg.isActive) : [];

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: packages
          })
        };
      } catch (error) {
        console.error('获取套餐失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取套餐失败'
          })
        };
      }
    }

    // 默认404响应
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        message: 'API endpoint not found',
        path: path,
        method: method
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
