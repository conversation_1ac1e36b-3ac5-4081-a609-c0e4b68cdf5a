const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试数据同步功能
async function testDataSync() {
  console.log('=== 腾讯云数据同步测试工具 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 1. 使用VIP用户的Token进行测试
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzMDMyNjE5fQ==';
    
    console.log('🔄 测试数据同步上传...');
    
    // 模拟大量数据（类似用户的19本小说和6个知识库文档）
    const novels = [];
    const knowledgeDocuments = [];

    // 生成19本小说，每本包含大量内容
    for (let i = 1; i <= 19; i++) {
      novels.push({
        id: `novel_${i.toString().padStart(3, '0')}`,
        title: `测试小说${i}`,
        author: `作者${i}`,
        content: '这是一个测试小说的内容，包含大量文字。'.repeat(200), // 模拟大量内容
        chapters: Array.from({length: 10}, (_, j) => ({
          id: `chapter_${i}_${j}`,
          title: `第${j+1}章`,
          content: '这是章节内容，包含大量文字。'.repeat(100)
        })),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    // 生成6个知识库文档
    for (let i = 1; i <= 6; i++) {
      knowledgeDocuments.push({
        id: `doc_${i.toString().padStart(3, '0')}`,
        title: `知识库文档${i}`,
        content: '这是知识库文档的内容，包含大量参考资料和设定信息。'.repeat(150),
        type: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    const syncData = {
      novels: novels,
      characterCards: [
        {
          id: 'char_001',
          name: '测试角色',
          description: '这是一个测试角色',
          novelId: 'novel_001',
          createdAt: new Date().toISOString()
        }
      ],
      knowledgeDocuments: knowledgeDocuments,
      userSettings: {
        theme: 'dark',
        autoSync: true,
        enableNotification: true
      }
    };

    // 计算数据大小
    const dataString = JSON.stringify(syncData);
    const dataSizeKB = Buffer.byteLength(dataString, 'utf8') / 1024;
    console.log(`📊 同步数据大小: ${dataSizeKB.toFixed(2)} KB`);

    // 上传数据
    const uploadResponse = await axios.post(`${BASE_URL}/sync/upload`, {
      data: syncData,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (uploadResponse.data.success) {
      console.log('✅ 数据同步上传成功:', uploadResponse.data.message);
      console.log('   时间戳:', uploadResponse.data.timestamp);
    } else {
      console.log('❌ 数据同步上传失败:', uploadResponse.data.message);
      return;
    }

    console.log('');
    console.log('📥 测试数据同步下载...');

    // 下载数据
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据同步下载成功:', downloadResponse.data.message);
      console.log('   时间戳:', downloadResponse.data.timestamp);
      console.log('   数据内容:');
      
      const data = downloadResponse.data.data;
      if (data.novels && data.novels.length > 0) {
        console.log(`   - 小说: ${data.novels.length} 本`);
        data.novels.forEach((novel, index) => {
          console.log(`     ${index + 1}. ${novel.title} (${novel.author})`);
        });
      }
      
      if (data.characterCards && data.characterCards.length > 0) {
        console.log(`   - 角色卡片: ${data.characterCards.length} 个`);
        data.characterCards.forEach((char, index) => {
          console.log(`     ${index + 1}. ${char.name}`);
        });
      }
      
      if (data.userSettings) {
        console.log('   - 用户设置:');
        console.log(`     主题: ${data.userSettings.theme}`);
        console.log(`     自动同步: ${data.userSettings.autoSync}`);
        console.log(`     通知: ${data.userSettings.enableNotification}`);
      }
    } else {
      console.log('❌ 数据同步下载失败:', downloadResponse.data.message);
    }

    console.log('');
    console.log('🚫 测试非会员用户同步...');

    // 使用普通用户Token测试
    const normalToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyOTcxODJfaXZ5eThhZHIxIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    try {
      const normalUploadResponse = await axios.post(`${BASE_URL}/sync/upload`, {
        data: { test: 'data' },
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${normalToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (normalUploadResponse.data.success) {
        console.log('❌ 意外：普通用户同步成功了（应该失败）');
      } else {
        console.log('✅ 正确：普通用户同步被拒绝:', normalUploadResponse.data.message);
      }
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log('✅ 正确：普通用户同步被拒绝 (403):', error.response.data.message);
      } else {
        console.log('❌ 普通用户测试出错:', error.message);
      }
    }

    console.log('');
    console.log('🎉 数据同步功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   请求URL:', error.config?.url);
    }
  }
}

// 测试无效Token
async function testInvalidToken() {
  console.log('');
  console.log('🔒 测试无效Token...');
  
  try {
    const response = await axios.post(`${BASE_URL}/sync/upload`, {
      data: { test: 'data' }
    }, {
      headers: {
        'Authorization': 'Bearer invalid-token',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 意外：无效Token请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ 正确：无效Token被拒绝 (401):', error.response.data.message);
    } else {
      console.log('❌ 无效Token测试出错:', error.message);
    }
  }
}

// 测试缺少Token
async function testMissingToken() {
  console.log('');
  console.log('🚫 测试缺少Token...');
  
  try {
    const response = await axios.post(`${BASE_URL}/sync/upload`, {
      data: { test: 'data' }
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('❌ 意外：缺少Token的请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ 正确：缺少Token被拒绝 (401):', error.response.data.message);
    } else {
      console.log('❌ 缺少Token测试出错:', error.message);
    }
  }
}

// 主函数
async function main() {
  await testDataSync();
  await testInvalidToken();
  await testMissingToken();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testDataSync, testInvalidToken, testMissingToken };
