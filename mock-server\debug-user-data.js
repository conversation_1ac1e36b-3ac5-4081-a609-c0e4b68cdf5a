const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 调试用户真实数据
async function debugUserData() {
  console.log('=== 用户真实数据调试 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('📥 下载当前云端数据...');
    
    // 下载数据
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据下载成功');
      console.log('   时间戳:', downloadResponse.data.timestamp);
      
      const data = downloadResponse.data.data;
      console.log('');
      console.log('📊 详细数据分析:');
      
      // 检查小说数据
      if (data.novels && data.novels.length > 0) {
        console.log(`\n📚 小说数据 (${data.novels.length} 本):`);
        data.novels.forEach((novel, index) => {
          console.log(`${index + 1}. "${novel.title}" by ${novel.author}`);
          console.log(`   ID: ${novel.id}`);
          console.log(`   创建时间: ${novel.createdAt}`);
          console.log(`   更新时间: ${novel.updatedAt}`);
          console.log(`   内容长度: ${novel.content ? novel.content.length : 0} 字符`);
          console.log('');
        });
        
        // 检查是否是用户的真实数据
        const userNovels = data.novels.filter(novel => 
          novel.title.includes('赛博朋克') || 
          novel.title.includes('大秦') || 
          novel.title.includes('神豪系统')
        );
        
        if (userNovels.length > 0) {
          console.log('✅ 发现用户真实小说数据!');
          userNovels.forEach(novel => {
            console.log(`   - ${novel.title}`);
          });
        } else {
          console.log('❌ 没有发现用户的真实小说数据');
          console.log('   当前云端的小说都是测试数据');
        }
      } else {
        console.log('❌ 云端没有小说数据');
      }
      
      // 检查角色卡片数据
      if (data.characterCards && data.characterCards.length > 0) {
        console.log(`\n👥 角色卡片数据 (${data.characterCards.length} 个):`);
        data.characterCards.forEach((card, index) => {
          console.log(`${index + 1}. "${card.name}"`);
          console.log(`   ID: ${card.id}`);
          console.log(`   描述: ${card.description || '无描述'}`);
          console.log(`   关联小说: ${card.novelId || '无关联'}`);
          console.log('');
        });
      } else {
        console.log('❌ 云端没有角色卡片数据');
      }
      
      // 检查角色类型数据
      if (data.characterTypes && data.characterTypes.length > 0) {
        console.log(`\n🏷️ 角色类型数据 (${data.characterTypes.length} 个):`);
        data.characterTypes.forEach((type, index) => {
          console.log(`${index + 1}. "${type.name}"`);
          console.log(`   ID: ${type.id}`);
          console.log(`   描述: ${type.description || '无描述'}`);
          console.log('');
        });
      } else {
        console.log('❌ 云端没有角色类型数据');
      }
      
      // 检查知识库文档
      if (data.knowledgeDocuments && data.knowledgeDocuments.length > 0) {
        console.log(`\n📖 知识库文档 (${data.knowledgeDocuments.length} 个):`);
        data.knowledgeDocuments.forEach((doc, index) => {
          console.log(`${index + 1}. "${doc.title}"`);
          console.log(`   ID: ${doc.id}`);
          console.log(`   类型: ${doc.type}`);
          console.log(`   内容长度: ${doc.content ? doc.content.length : 0} 字符`);
          console.log('');
        });
      } else {
        console.log('❌ 云端没有知识库文档');
      }
      
      // 检查风格包
      if (data.stylePackages && data.stylePackages.length > 0) {
        console.log(`\n🎨 风格包数据 (${data.stylePackages.length} 个):`);
        data.stylePackages.forEach((pkg, index) => {
          console.log(`${index + 1}. "${pkg.name}"`);
          console.log(`   ID: ${pkg.id}`);
          console.log(`   描述: ${pkg.description || '无描述'}`);
          console.log('');
        });
      } else {
        console.log('❌ 云端没有风格包数据');
      }

    } else {
      console.log('❌ 数据下载失败:', downloadResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 调试失败:', error.response?.data || error.message);
  }
}

// 清除测试数据，准备接收用户真实数据
async function clearTestData() {
  console.log('\n=== 清除测试数据 ===');
  
  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  try {
    // 上传空数据来清除云端数据
    const response = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'full',
      data: {
        novels: [],
        characterCards: [],
        characterTypes: [],
        knowledgeDocuments: [],
        stylePackages: [],
        userSettings: {
          theme: 'light',
          autoSync: true,
          enableNotification: true
        }
      },
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ 测试数据已清除，云端现在是空的');
      console.log('💡 现在可以重新进行用户数据同步测试');
    } else {
      console.log('❌ 清除失败:', response.data.message);
    }
  } catch (error) {
    console.log('❌ 清除异常:', error.response?.data?.message || error.message);
  }
}

// 运行调试
async function main() {
  await debugUserData();
  
  console.log('\n🎯 问题分析:');
  console.log('如果云端只有测试数据，说明用户的真实数据没有成功上传');
  console.log('可能的原因:');
  console.log('1. 数据上传被测试数据覆盖了');
  console.log('2. 数据格式不正确，上传失败');
  console.log('3. 数据收集阶段有问题');
  
  console.log('\n💡 解决方案:');
  console.log('1. 清除云端测试数据');
  console.log('2. 重新进行用户数据同步');
  console.log('3. 仔细观察上传过程的日志');
  
  // 询问是否清除测试数据
  console.log('\n❓ 是否要清除云端测试数据？');
  console.log('   如果要清除，请运行: node debug-user-data.js clear');
}

// 检查命令行参数
if (process.argv.includes('clear')) {
  clearTestData().catch(console.error);
} else {
  main().catch(console.error);
}

module.exports = { debugUserData, clearTestData };
