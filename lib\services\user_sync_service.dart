import 'dart:convert';
import 'dart:math' as Math;
import 'package:dio/dio.dart' as dio_pkg;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/novel.dart';
import '../models/character_card.dart';
import '../models/character_type.dart';
import '../models/knowledge_document.dart';
import '../models/writing_style_package.dart';
import '../config/api_config.dart';
import '../controllers/novel_controller.dart';
import '../controllers/knowledge_base_controller.dart';
import '../controllers/writing_style_package_controller.dart';
import '../controllers/character_card_controller.dart';
import '../controllers/character_type_controller.dart';
import '../controllers/style_controller.dart';
import 'auth_service.dart';

/// 用户数据同步服务
class UserSyncService extends GetxService {
  static const String _syncEnabledKey = 'sync_enabled';
  static const String _lastSyncTimeKey = 'last_sync_time';

  final dio_pkg.Dio _dio = dio_pkg.Dio();
  final AuthService _authService = Get.find<AuthService>();
  
  final RxBool isSyncEnabled = true.obs;
  final RxBool isSyncing = false.obs;
  final Rx<DateTime?> lastSyncTime = Rx<DateTime?>(null);

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _loadSyncSettings();
    
    // 监听用户登录状态变化
    ever(_authService.isLoggedIn, (isLoggedIn) {
      if (isLoggedIn && isSyncEnabled.value) {
        // 用户登录后自动同步
        syncUserData();
      }
    });
  }

  /// 加载同步设置
  void _loadSyncSettings() {
    isSyncEnabled.value = _prefs?.getBool(_syncEnabledKey) ?? true;
    final lastSyncStr = _prefs?.getString(_lastSyncTimeKey);
    if (lastSyncStr != null) {
      lastSyncTime.value = DateTime.parse(lastSyncStr);
    }
  }

  /// 设置同步开关
  Future<void> setSyncEnabled(bool enabled) async {
    // 检查用户是否为会员
    final user = _authService.currentUser.value;
    if (enabled && (user == null || !user.isValidMember)) {
      Get.snackbar('权限不足', '数据同步功能仅限会员使用');
      return;
    }

    isSyncEnabled.value = enabled;
    await _prefs?.setBool(_syncEnabledKey, enabled);

    if (enabled && _authService.isLoggedIn.value) {
      // 启用同步时立即同步一次
      await syncUserData();
    }
  }

  /// 同步用户数据
  Future<bool> syncUserData() async {
    if (!_authService.isLoggedIn.value || !isSyncEnabled.value) {
      return false;
    }

    // 检查用户是否为会员，非会员不能使用数据同步
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      print('数据同步失败: 非会员用户无法使用数据同步功能');
      Get.snackbar('权限不足', '数据同步功能仅限会员使用');
      return false;
    }

    try {
      isSyncing.value = true;
      
      // 获取本地数据
      final localData = await _collectLocalData();
      
      // 获取认证Token
      final token = await _getToken();
      if (token == null) {
        print('同步失败: 未找到认证Token');
        return false;
      }

      // 使用分批上传到服务器
      final success = await _uploadDataInBatches(localData, token);

      if (success) {
        // 下载服务器数据
        await _downloadServerData();
        
        // 更新最后同步时间
        lastSyncTime.value = DateTime.now();
        await _prefs?.setString(_lastSyncTimeKey, lastSyncTime.value!.toIso8601String());
        
        // 显示同步成功的详细信息
        final localData = await _collectLocalData();
        final novelsCount = (localData['novels'] as List?)?.length ?? 0;
        final charactersCount = (localData['characterCards'] as List?)?.length ?? 0;
        final typesCount = (localData['characterTypes'] as List?)?.length ?? 0;
        final docsCount = (localData['knowledgeDocuments'] as List?)?.length ?? 0;
        final stylesCount = (localData['stylePackages'] as List?)?.length ?? 0;

        final syncSummary = [
          if (novelsCount > 0) '$novelsCount本小说',
          if (charactersCount > 0) '$charactersCount个角色',
          if (typesCount > 0) '$typesCount个类型',
          if (docsCount > 0) '$docsCount个文档',
          if (stylesCount > 0) '$stylesCount个风格包',
        ].join('，');

        final message = syncSummary.isNotEmpty ? '数据同步完成：$syncSummary' : '数据同步完成';
        Get.snackbar('成功', message);
        return true;
      } else {
        Get.snackbar('错误', '数据同步失败');
        return false;
      }
    } catch (e) {
      print('数据同步失败: $e');
      Get.snackbar('错误', '数据同步失败: ${e.toString()}');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 收集本地数据
  Future<Map<String, dynamic>> _collectLocalData() async {
    final data = <String, dynamic>{};
    
    try {
      // 收集小说数据
      try {
        print('🔍 检查NovelController是否注册: ${Get.isRegistered<NovelController>()}');
        if (Get.isRegistered<NovelController>()) {
          final novelController = Get.find<NovelController>();
          print('📚 NovelController找到，小说数量: ${novelController.novels.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (novelController.novels.isEmpty) {
            print('⚠️ NovelController中没有数据，尝试重新加载...');
            try {
              await novelController.loadNovels(); // 这是异步方法，需要await
              print('🔄 重新加载后小说数量: ${novelController.novels.length}');

              // 等待一小段时间确保数据完全加载
              await Future.delayed(Duration(milliseconds: 100));
              print('🔄 延迟后小说数量: ${novelController.novels.length}');
            } catch (e) {
              print('❌ 重新加载小说失败: $e');
            }
          }

          final novelsData = novelController.novels.map((novel) => novel.toJson()).toList();
          data['novels'] = novelsData;
          print('✅ 收集到 ${novelsData.length} 本小说数据');

          // 打印前几本小说的标题用于调试
          if (novelsData.isNotEmpty) {
            print('   前几本小说:');
            for (int i = 0; i < Math.min(3, novelsData.length); i++) {
              print('   ${i + 1}. ${novelsData[i]['title']}');
            }
          } else {
            print('⚠️ 警告：应用中有19本小说，但NovelController中为空！');
            print('   这可能是数据加载时机问题');
          }
        } else {
          print('❌ NovelController未注册');
          data['novels'] = [];
        }
      } catch (e) {
        print('❌ 收集小说数据失败: $e');
        data['novels'] = [];
      }

      // 收集角色卡片数据
      try {
        print('🔍 检查CharacterCardController是否注册: ${Get.isRegistered<CharacterCardController>()}');
        if (Get.isRegistered<CharacterCardController>()) {
          final characterCardController = Get.find<CharacterCardController>();
          print('👥 CharacterCardController找到，角色卡片数量: ${characterCardController.characterCards.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (characterCardController.characterCards.isEmpty) {
            print('⚠️ CharacterCardController中没有数据，尝试重新加载...');
            try {
              characterCardController.loadCharacterCards();
              print('🔄 重新加载后角色卡片数量: ${characterCardController.characterCards.length}');
            } catch (e) {
              print('❌ 重新加载角色卡片失败: $e');
            }
          }

          final characterCards = characterCardController.characterCards
              .map((card) => card.toJson())
              .toList();

          data['characterCards'] = characterCards;
          print('✅ 收集到 ${characterCards.length} 个角色卡片');

          // 打印前几个角色卡片的名称用于调试
          if (characterCards.isNotEmpty) {
            print('   前几个角色卡片:');
            for (int i = 0; i < Math.min(3, characterCards.length); i++) {
              print('   ${i + 1}. ${characterCards[i]['name']}');
            }
          }
        } else {
          data['characterCards'] = [];
          print('❌ 角色卡片控制器未初始化，跳过收集');
        }
      } catch (e) {
        print('❌ 收集角色卡片数据失败: $e');
        data['characterCards'] = [];
      }

      // 收集自定义角色类型
      try {
        print('🔍 检查CharacterTypeController是否注册: ${Get.isRegistered<CharacterTypeController>()}');
        if (Get.isRegistered<CharacterTypeController>()) {
          final characterTypeController = Get.find<CharacterTypeController>();
          print('🏷️ CharacterTypeController找到，角色类型数量: ${characterTypeController.characterTypes.length}');

          // 如果控制器中没有数据，尝试重新加载
          if (characterTypeController.characterTypes.isEmpty) {
            print('⚠️ CharacterTypeController中没有数据，尝试重新加载...');
            try {
              characterTypeController.loadCharacterTypes();
              print('🔄 重新加载后角色类型数量: ${characterTypeController.characterTypes.length}');
            } catch (e) {
              print('❌ 重新加载角色类型失败: $e');
            }
          }

          final characterTypes = characterTypeController.characterTypes
              .map((type) => type.toJson())
              .toList();
          data['characterTypes'] = characterTypes;
          print('✅ 收集到 ${characterTypes.length} 个角色类型');

          // 打印前几个角色类型的名称用于调试
          if (characterTypes.isNotEmpty) {
            print('   前几个角色类型:');
            for (int i = 0; i < Math.min(3, characterTypes.length); i++) {
              print('   ${i + 1}. ${characterTypes[i]['name']}');
            }
          }
        } else {
          data['characterTypes'] = [];
          print('❌ 角色类型控制器未初始化，跳过收集');
        }
      } catch (e) {
        print('❌ 收集角色类型数据失败: $e');
        data['characterTypes'] = [];
      }

      // 收集知识库文档（排除官方只读内容）
      try {
        final knowledgeController = Get.find<KnowledgeBaseController>();

        // 过滤掉官方只读文档
        final userDocuments = knowledgeController.documents.where((doc) {
          // 排除只读文档
          if (doc.isReadOnly) return false;

          // 排除包含"官方"标签的文档
          if (doc.tags.contains('官方')) return false;

          // 排除默认文档
          if (doc.isDefault) return false;

          return true;
        }).toList();

        data['knowledgeDocuments'] = userDocuments.map((doc) => doc.toJson()).toList();
        print('收集到 ${userDocuments.length} 个用户知识库文档（已排除 ${knowledgeController.documents.length - userDocuments.length} 个官方文档）');
      } catch (e) {
        print('收集知识库数据失败: $e');
        data['knowledgeDocuments'] = [];
      }

      // 收集写作风格包
      try {
        final styleController = Get.find<WritingStylePackageController>();
        data['stylePackages'] = styleController.packages.map((pkg) => pkg.toJson()).toList();
      } catch (e) {
        print('收集风格包数据失败: $e');
        data['stylePackages'] = [];
      }

      // 收集用户设置
      try {
        print('🔍 检查用户设置...');
        final currentUser = _authService.currentUser.value;
        if (currentUser != null && currentUser.settings != null) {
          data['userSettings'] = currentUser.settings.toJson();
          print('✅ 收集到用户设置数据');
        } else {
          data['userSettings'] = null;
          print('❌ 用户设置为空');
        }
      } catch (e) {
        print('❌ 收集用户设置失败: $e');
        data['userSettings'] = null;
      }

    } catch (e) {
      print('收集本地数据失败: $e');
    }
    
    return data;
  }

  /// 从云端下载数据（公开方法，供登录后调用）
  Future<bool> downloadFromCloud() async {
    if (!_authService.isLoggedIn.value) {
      print('下载失败: 用户未登录');
      return false;
    }

    // 检查用户是否为会员
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      print('下载失败: 非会员用户无法使用数据同步功能');
      return false;
    }

    // 确保必要的控制器已注册
    _ensureControllersRegistered();

    try {
      isSyncing.value = true;
      print('📥 开始从云端下载数据...');

      final token = await _getToken();
      if (token == null) {
        print('下载失败: 未找到认证Token');
        return false;
      }

      final response = await _dio.get(
        '${ApiConfig.baseUrl}/sync/download',
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.data['success'] == true) {
        final serverData = response.data['data'];
        final timestamp = response.data['timestamp'];

        print('✅ 云端数据下载成功');
        print('   同步时间戳: $timestamp');

        // 检查是否有数据
        bool hasData = false;
        if (serverData['novels'] != null && (serverData['novels'] as List).isNotEmpty) {
          print('   - 小说: ${(serverData['novels'] as List).length} 本');
          hasData = true;
        }
        if (serverData['knowledgeDocuments'] != null && (serverData['knowledgeDocuments'] as List).isNotEmpty) {
          print('   - 知识库文档: ${(serverData['knowledgeDocuments'] as List).length} 个');
          hasData = true;
        }
        if (serverData['characterCards'] != null && (serverData['characterCards'] as List).isNotEmpty) {
          print('   - 角色卡片: ${(serverData['characterCards'] as List).length} 个');
          hasData = true;
        }

        if (hasData) {
          await _applyServerData(serverData);

          // 更新最后同步时间
          lastSyncTime.value = DateTime.now();
          await _prefs?.setString(_lastSyncTimeKey, lastSyncTime.value!.toIso8601String());

          Get.snackbar('同步成功', '云端数据已同步到本地');
          print('🎉 数据同步完成！');
        } else {
          print('ℹ️ 云端暂无数据');
          Get.snackbar('提示', '云端暂无同步数据');
        }

        return true;
      } else {
        print('下载失败: ${response.data['message']}');
        Get.snackbar('同步失败', response.data['message'] ?? '下载失败');
        return false;
      }
    } catch (e) {
      print('下载云端数据失败: $e');
      Get.snackbar('同步失败', '网络错误，请稍后重试');
      return false;
    } finally {
      isSyncing.value = false;
    }
  }

  /// 下载服务器数据（内部方法）
  Future<void> _downloadServerData() async {
    await downloadFromCloud();
  }

  /// 应用服务器数据
  Future<void> _applyServerData(Map<String, dynamic> serverData) async {
    try {
      // 应用小说数据
      if (serverData['novels'] != null) {
        try {
          final novelController = Get.find<NovelController>();
          final serverNovels = (serverData['novels'] as List)
              .map((json) {
                try {
                  // 确保必要字段不为null
                  if (json['id'] == null) json['id'] = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
                  if (json['title'] == null) json['title'] = '未命名小说';
                  if (json['author'] == null) json['author'] = '未知作者';
                  if (json['content'] == null) json['content'] = '';
                  if (json['createdAt'] == null) json['createdAt'] = DateTime.now().toIso8601String();
                  if (json['updatedAt'] == null) json['updatedAt'] = DateTime.now().toIso8601String();

                  return Novel.fromJson(json);
                } catch (e) {
                  print('⚠️ 跳过无效小说数据: $e');
                  return null;
                }
              })
              .where((novel) => novel != null)
              .cast<Novel>()
              .toList();
          await _mergeNovels(novelController, serverNovels);
          print('✅ 应用了 ${serverNovels.length} 个小说数据');
        } catch (e) {
          print('❌ 应用小说数据失败: $e');
        }
      }

      // 应用角色卡片数据
      if (serverData['characterCards'] != null) {
        try {
          if (Get.isRegistered<CharacterCardController>()) {
            final characterCardController = Get.find<CharacterCardController>();
            final serverCards = (serverData['characterCards'] as List)
                .map((json) => CharacterCard.fromJson(json))
                .toList();
            await _mergeCharacterCards(characterCardController, serverCards);
            print('✅ 应用了 ${serverCards.length} 个角色卡片数据');
          } else {
            print('⚠️ 角色卡片控制器未初始化，跳过应用角色卡片数据');
          }
        } catch (e) {
          print('❌ 应用角色卡片数据失败: $e');
        }
      }

      // 应用自定义角色类型
      if (serverData['characterTypes'] != null) {
        try {
          if (Get.isRegistered<CharacterTypeController>()) {
            final characterTypeController = Get.find<CharacterTypeController>();
            final serverTypes = (serverData['characterTypes'] as List)
                .map((json) => CharacterType.fromJson(json))
                .toList();
            await _mergeCharacterTypes(characterTypeController, serverTypes);
            print('✅ 应用了 ${serverTypes.length} 个角色类型数据');
          } else {
            print('⚠️ 角色类型控制器未初始化，跳过应用角色类型数据');
          }
        } catch (e) {
          print('❌ 应用角色类型数据失败: $e');
        }
      }

      // 应用知识库文档
      if (serverData['knowledgeDocuments'] != null) {
        try {
          final knowledgeController = Get.find<KnowledgeBaseController>();
          final serverDocs = (serverData['knowledgeDocuments'] as List)
              .map((json) {
                try {
                  // 确保必要字段不为null
                  if (json['id'] == null) json['id'] = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
                  if (json['title'] == null) json['title'] = '未命名文档';
                  if (json['content'] == null) json['content'] = '';
                  if (json['type'] == null) json['type'] = 'user';
                  if (json['createdAt'] == null) json['createdAt'] = DateTime.now().toIso8601String();
                  if (json['updatedAt'] == null) json['updatedAt'] = DateTime.now().toIso8601String();

                  return KnowledgeDocument.fromJson(json);
                } catch (e) {
                  print('⚠️ 跳过无效知识库文档: $e');
                  return null;
                }
              })
              .where((doc) => doc != null)
              .cast<KnowledgeDocument>()
              .toList();
          await _mergeKnowledgeDocuments(knowledgeController, serverDocs);
          print('✅ 应用了 ${serverDocs.length} 个知识库文档');
        } catch (e) {
          print('❌ 应用知识库数据失败: $e');
        }
      }

      // 应用写作风格包
      if (serverData['stylePackages'] != null) {
        try {
          final styleController = Get.find<WritingStylePackageController>();
          final serverPackages = (serverData['stylePackages'] as List)
              .map((json) => WritingStylePackage.fromJson(json))
              .toList();
          await _mergeStylePackages(styleController, serverPackages);
        } catch (e) {
          print('应用风格包数据失败: $e');
        }
      }
      
      // 应用用户设置
      if (serverData['userSettings'] != null) {
        final settings = UserSettings.fromJson(serverData['userSettings']);
        if (_authService.currentUser.value != null) {
          _authService.currentUser.value!.settings = settings;
          // 保存到本地
          await _prefs?.setString('user_data', jsonEncode(_authService.currentUser.value!.toJson()));
        }
      }
      
    } catch (e) {
      print('应用服务器数据失败: $e');
    }
  }

  /// 合并小说数据
  Future<void> _mergeNovels(NovelController controller, List<Novel> serverNovels) async {
    // 实现数据合并逻辑，以最新更新时间为准
    for (final serverNovel in serverNovels) {
      final localIndex = controller.novels.indexWhere((n) => n.id == serverNovel.id);
      if (localIndex == -1) {
        // 本地没有，直接添加
        controller.novels.add(serverNovel);
      } else {
        // 本地有，比较更新时间
        final localNovel = controller.novels[localIndex];
        if (serverNovel.updatedAt != null &&
            (localNovel.updatedAt == null || serverNovel.updatedAt!.isAfter(localNovel.updatedAt!))) {
          controller.novels[localIndex] = serverNovel;
        }
      }
    }
    // 保存小说数据
    try {
      final novelController = Get.find<NovelController>();
      // NovelController没有saveAllNovels方法，但小说数据已经自动保存到Hive
      print('小说数据已自动保存到本地存储');
    } catch (e) {
      print('保存小说数据失败: $e');
    }
  }

  /// 合并角色卡片数据
  Future<void> _mergeCharacterCards(CharacterCardController controller, List<CharacterCard> serverCards) async {
    try {
      for (final serverCard in serverCards) {
        final localIndex = controller.characterCards.indexWhere((c) => c.id == serverCard.id);
        if (localIndex == -1) {
          // 本地没有，直接添加
          controller.characterCards.add(serverCard);
          print('添加新角色卡片: ${serverCard.name}');
        } else {
          // 本地有，直接替换（因为CharacterCard没有updatedAt字段）
          controller.characterCards[localIndex] = serverCard;
          print('更新角色卡片: ${serverCard.name}');
        }
      }
      // 触发UI更新
      controller.characterCards.refresh();
      print('角色卡片数据已应用到控制器');
    } catch (e) {
      print('角色卡片数据合并失败: $e');
    }
  }

  /// 合并角色类型数据
  Future<void> _mergeCharacterTypes(CharacterTypeController controller, List<CharacterType> serverTypes) async {
    try {
      for (final serverType in serverTypes) {
        final localIndex = controller.characterTypes.indexWhere((t) => t.id == serverType.id);
        if (localIndex == -1) {
          // 本地没有，直接添加
          controller.characterTypes.add(serverType);
          print('添加新角色类型: ${serverType.name}');
        } else {
          // 本地有，直接替换（因为CharacterType没有updatedAt字段）
          controller.characterTypes[localIndex] = serverType;
          print('更新角色类型: ${serverType.name}');
        }
      }
      // 触发UI更新
      controller.characterTypes.refresh();
      print('角色类型数据已应用到控制器');
    } catch (e) {
      print('角色类型数据合并失败: $e');
    }
  }

  /// 合并知识库文档数据
  Future<void> _mergeKnowledgeDocuments(KnowledgeBaseController controller, List<KnowledgeDocument> serverDocs) async {
    for (final serverDoc in serverDocs) {
      final localIndex = controller.documents.indexWhere((d) => d.id == serverDoc.id);
      if (localIndex == -1) {
        controller.documents.add(serverDoc);
      } else {
        final localDoc = controller.documents[localIndex];
        if (serverDoc.updatedAt.isAfter(localDoc.updatedAt)) {
          controller.documents[localIndex] = serverDoc;
        }
      }
    }
    // 保存知识库数据
    try {
      // 知识库数据已经在上面的循环中逐个处理了
      print('知识库数据同步完成');
    } catch (e) {
      print('保存知识库数据失败: $e');
    }
  }

  /// 合并写作风格包数据
  Future<void> _mergeStylePackages(WritingStylePackageController controller, List<WritingStylePackage> serverPackages) async {
    for (final serverPackage in serverPackages) {
      final localIndex = controller.packages.indexWhere((p) => p.id == serverPackage.id);
      if (localIndex == -1) {
        controller.packages.add(serverPackage);
      } else {
        controller.packages[localIndex] = serverPackage;
      }
    }
    // 保存风格包数据
    try {
      if (Get.isRegistered<StyleController>()) {
        final styleController = Get.find<StyleController>();
        // StyleController的数据已经自动保存到SharedPreferences
        print('风格包数据已自动保存到本地存储');
      } else {
        print('风格包控制器未初始化，跳过保存');
      }
    } catch (e) {
      print('保存风格包数据失败: $e');
    }
  }



  /// 导出用户数据
  Future<Map<String, dynamic>> exportUserData() async {
    return await _collectLocalData();
  }

  /// 导入用户数据
  Future<bool> importUserData(Map<String, dynamic> data) async {
    try {
      await _applyServerData(data);
      Get.snackbar('成功', '数据导入完成');
      return true;
    } catch (e) {
      print('数据导入失败: $e');
      Get.snackbar('错误', '数据导入失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取当前用户Token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  /// 分批上传数据
  Future<bool> _uploadDataInBatches(Map<String, dynamic> localData, String token) async {
    try {
      final timestamp = DateTime.now().toIso8601String();

      // 1. 分批上传小说数据
      final novels = localData['novels'] as List? ?? [];
      if (novels.isNotEmpty) {
        print('开始分批上传 ${novels.length} 本小说...');
        final success = await _uploadNovelsInBatches(novels, token, timestamp);
        if (!success) return false;
      }

      // 2. 上传知识库文档
      final knowledgeDocuments = localData['knowledgeDocuments'] as List? ?? [];
      if (knowledgeDocuments.isNotEmpty) {
        print('上传 ${knowledgeDocuments.length} 个知识库文档...');
        final compressedDocs = _compressKnowledgeDocuments(knowledgeDocuments);
        final success = await _uploadSingleDataType('knowledgeDocuments', compressedDocs, token, timestamp);
        if (!success) return false;
      }

      // 3. 上传角色卡片
      final characterCards = localData['characterCards'] as List? ?? [];
      if (characterCards.isNotEmpty) {
        print('上传 ${characterCards.length} 个角色卡片...');
        final success = await _uploadSingleDataType('characterCards', characterCards, token, timestamp);
        if (!success) return false;
      }

      // 4. 上传角色类型
      final characterTypes = localData['characterTypes'] as List? ?? [];
      if (characterTypes.isNotEmpty) {
        print('上传 ${characterTypes.length} 个角色类型...');
        final success = await _uploadSingleDataType('characterTypes', characterTypes, token, timestamp);
        if (!success) return false;
      }

      // 5. 上传风格包
      final stylePackages = localData['stylePackages'] as List? ?? [];
      if (stylePackages.isNotEmpty) {
        print('上传 ${stylePackages.length} 个风格包...');
        final success = await _uploadSingleDataType('stylePackages', stylePackages, token, timestamp);
        if (!success) return false;
      }

      // 6. 上传用户设置
      final userSettings = localData['userSettings'];
      if (userSettings != null) {
        print('上传用户设置...');
        final success = await _uploadSingleDataType('userSettings', userSettings, token, timestamp);
        if (!success) return false;
      }

      print('所有数据上传完成！');
      return true;
    } catch (e) {
      print('分批上传失败: $e');
      return false;
    }
  }

  /// 直接上传完整小说数据（不压缩，不分批）
  Future<bool> _uploadNovelsInBatches(List novels, String token, String timestamp) async {
    print('🚀 开始上传完整小说数据（不压缩，不分批）...');
    print('📊 小说数量: ${novels.length} 本');

    // 打印每本小说的详细信息
    for (int i = 0; i < novels.length; i++) {
      final novel = novels[i];
      final novelSize = jsonEncode(novel).length;
      print('   📖 小说 ${i+1}: "${novel['title']}" - ${novelSize} 字节');
      print('      作者: ${novel['author'] ?? '未知'}');
      print('      内容长度: ${novel['content']?.length ?? 0} 字符');
      print('      章节数: ${novel['chapters']?.length ?? 0}');
      print('      创建时间: ${novel['createdAt']}');
      print('      ID: ${novel['id']}');
    }

    // 构建完整的请求数据（不压缩）
    final requestData = {
      'dataType': 'novels',
      'data': {'novels': novels}, // 直接使用原始数据，不压缩
      'batchInfo': {
        'isComplete': true,
        'batchId': null,
        'batchIndex': 0,
        'totalBatches': 1,
      },
      'timestamp': timestamp,
    };

    // 计算完整请求数据大小
    final requestDataString = jsonEncode(requestData);
    final requestSizeKB = requestDataString.length / 1024;
    final requestSizeMB = requestSizeKB / 1024;

    print('📊 完整请求数据大小: ${requestDataString.length} 字节');
    print('📊 完整请求数据大小: ${requestSizeKB.toFixed(2)} KB');
    print('📊 完整请求数据大小: ${requestSizeMB.toFixed(2)} MB');

    if (requestSizeMB > 10) {
      print('⚠️ 警告：数据大小超过10MB，可能会上传失败');
    }

    try {
      print('🚀 开始上传到服务器...');
      print('🌐 API地址: ${ApiConfig.baseUrl}/sync/upload');

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload',
        data: requestData,
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
          sendTimeout: Duration(minutes: 5), // 增加超时时间
          receiveTimeout: Duration(minutes: 5),
        ),
      );

      print('📡 服务器响应状态: ${response.statusCode}');
      print('📡 服务器响应数据: ${response.data}');

      if (response.data['success'] == true) {
        print('✅ 完整小说数据上传成功: ${response.data['message']}');
        print('🎉 所有 ${novels.length} 本小说已成功上传到云端！');
        return true;
      } else {
        print('❌ 小说数据上传失败: ${response.data['message']}');
        print('❌ 错误详情: ${response.data}');
        return false;
      }
    } catch (e) {
      print('❌ 小说数据上传异常: $e');
      if (e.toString().contains('timeout')) {
        print('❌ 上传超时，可能是数据太大或网络问题');
      }
      if (e.toString().contains('413')) {
        print('❌ 请求实体太大，服务器拒绝处理');
      }
      if (e.toString().contains('500')) {
        print('❌ 服务器内部错误，可能是数据格式问题');
      }
      return false;
    }
  }

  /// 上传单一数据类型
  Future<bool> _uploadSingleDataType(String dataType, dynamic data, String token, String timestamp) async {
    try {
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/sync/upload',
        data: {
          'dataType': dataType,
          'data': {dataType: data},
          'batchInfo': {
            'isComplete': true,
            'batchId': null,
            'batchIndex': 0,
            'totalBatches': 1,
          },
          'timestamp': timestamp,
        },
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer $token'},
        ),
      );

      if (response.data['success'] == true) {
        print('✅ ${response.data['message']}');
        return true;
      } else {
        print('❌ $dataType 上传失败: ${response.data['message']}');
        return false;
      }
    } catch (e) {
      print('❌ $dataType 上传异常: $e');
      return false;
    }
  }

  /// 压缩小说数据
  List<Map<String, dynamic>> _compressNovels(List novels) {
    return novels.map<Map<String, dynamic>>((novel) {
      final novelMap = <String, dynamic>{};

      // 只保留最关键的字段
      novelMap['id'] = novel['id'] ?? 'unknown';
      novelMap['title'] = novel['title'] ?? '未命名';
      novelMap['author'] = novel['author'] ?? '未知作者';

      // 极度压缩内容字段，只保留前50个字符
      if (novel['content'] is String) {
        final content = novel['content'] as String;
        novelMap['content'] = content.length > 50
            ? content.substring(0, 50) + '...'
            : content;
      } else {
        novelMap['content'] = '无内容';
      }

      // 不包含章节数据，减少数据量
      novelMap['chapters'] = [];

      // 只保留时间戳
      novelMap['createdAt'] = novel['createdAt'];
      novelMap['updatedAt'] = novel['updatedAt'];

      return novelMap;
    }).toList();
  }

  /// 创建最小化小说数据（只保留ID和标题）
  List<Map<String, dynamic>> _createMinimalNovels(List novels) {
    return novels.map<Map<String, dynamic>>((novel) {
      return {
        'id': novel['id'] ?? 'unknown',
        'title': novel['title'] ?? '未命名',
        'author': novel['author'] ?? '未知',
        'content': '...',
        'chapters': [],
        'createdAt': novel['createdAt'],
        'updatedAt': novel['updatedAt'],
      };
    }).toList();
  }

  /// 压缩知识库文档数据
  List<Map<String, dynamic>> _compressKnowledgeDocuments(List documents) {
    return documents.map<Map<String, dynamic>>((doc) {
      final docMap = <String, dynamic>{};

      // 只保留最关键的字段
      docMap['id'] = doc['id'];
      docMap['title'] = doc['title'];
      docMap['type'] = doc['type'];
      docMap['createdAt'] = doc['createdAt'];
      docMap['updatedAt'] = doc['updatedAt'];

      // 极度压缩内容字段，只保留前100个字符
      if (doc['content'] is String) {
        final content = doc['content'] as String;
        docMap['content'] = content.length > 100
            ? content.substring(0, 100) + '...'
            : content;
      } else {
        docMap['content'] = '';
      }

      // 保留标签但限制数量
      if (doc['tags'] is List) {
        final tags = doc['tags'] as List;
        docMap['tags'] = tags.take(3).toList();
      } else {
        docMap['tags'] = [];
      }

      return docMap;
    }).toList();
  }

  /// 确保必要的控制器已注册
  void _ensureControllersRegistered() {
    try {
      // 确保角色卡片控制器注册
      if (!Get.isRegistered<CharacterCardController>()) {
        print('🔧 注册CharacterCardController...');
        Get.put(CharacterCardController());
      }

      // 确保角色类型控制器注册
      if (!Get.isRegistered<CharacterTypeController>()) {
        print('🔧 注册CharacterTypeController...');
        Get.put(CharacterTypeController());
      }

      print('✅ 所有必要控制器已确保注册');
    } catch (e) {
      print('⚠️ 控制器注册失败: $e');
    }
  }
}

extension DoubleExtension on double {
  String toFixed(int digits) => toStringAsFixed(digits);
}
