const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试分批数据同步功能
async function testBatchSync() {
  console.log('=== 腾讯云分批数据同步测试工具 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token进行测试
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    // 生成大量小说数据，分批上传
    const novels = [];
    for (let i = 1; i <= 19; i++) {
      novels.push({
        id: `novel_${i.toString().padStart(3, '0')}`,
        title: `测试小说${i}`,
        author: `作者${i}`,
        content: '这是一个测试小说的内容。'.repeat(50), // 减少内容大小
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    // 生成知识库文档
    const knowledgeDocuments = [];
    for (let i = 1; i <= 6; i++) {
      knowledgeDocuments.push({
        id: `doc_${i.toString().padStart(3, '0')}`,
        title: `知识库文档${i}`,
        content: '这是知识库文档的内容。'.repeat(30),
        type: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    console.log('🔄 测试分批数据同步上传...');
    console.log(`📊 小说数量: ${novels.length}, 知识库文档数量: ${knowledgeDocuments.length}`);

    // 分批上传小说（每批5本）
    const novelBatchSize = 5;
    const novelBatches = [];
    for (let i = 0; i < novels.length; i += novelBatchSize) {
      novelBatches.push(novels.slice(i, i + novelBatchSize));
    }

    console.log(`📦 小说分为 ${novelBatches.length} 批上传...`);

    for (let i = 0; i < novelBatches.length; i++) {
      const batch = novelBatches[i];
      const batchInfo = {
        isComplete: i === novelBatches.length - 1,
        batchId: 'novels_batch_' + Date.now(),
        batchIndex: i,
        totalBatches: novelBatches.length
      };

      console.log(`   上传小说批次 ${i + 1}/${novelBatches.length} (${batch.length} 本小说)...`);

      const response = await axios.post(`${BASE_URL}/sync/upload`, {
        dataType: 'novels',
        data: { novels: batch },
        batchInfo: batchInfo,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${vipToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log(`   ✅ ${response.data.message}`);
      } else {
        console.log(`   ❌ 批次 ${i + 1} 上传失败:`, response.data.message);
        return;
      }
    }

    // 上传知识库文档（一次性上传）
    console.log(`📚 上传知识库文档...`);
    const docResponse = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'knowledgeDocuments',
      data: { knowledgeDocuments: knowledgeDocuments },
      batchInfo: { isComplete: true, batchId: null, batchIndex: 0, totalBatches: 1 },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (docResponse.data.success) {
      console.log(`   ✅ ${docResponse.data.message}`);
    } else {
      console.log(`   ❌ 知识库文档上传失败:`, docResponse.data.message);
      return;
    }

    // 上传用户设置
    console.log(`⚙️ 上传用户设置...`);
    const settingsResponse = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'userSettings',
      data: { 
        userSettings: {
          theme: 'dark',
          autoSync: true,
          enableNotification: true
        }
      },
      batchInfo: { isComplete: true, batchId: null, batchIndex: 0, totalBatches: 1 },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (settingsResponse.data.success) {
      console.log(`   ✅ ${settingsResponse.data.message}`);
    } else {
      console.log(`   ❌ 用户设置上传失败:`, settingsResponse.data.message);
      return;
    }

    console.log('');
    console.log('📥 测试数据同步下载...');

    // 下载数据
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据同步下载成功:', downloadResponse.data.message);
      console.log('   时间戳:', downloadResponse.data.timestamp);
      
      const data = downloadResponse.data.data;
      if (data.novels && data.novels.length > 0) {
        console.log(`   - 小说: ${data.novels.length} 本`);
      }
      
      if (data.knowledgeDocuments && data.knowledgeDocuments.length > 0) {
        console.log(`   - 知识库文档: ${data.knowledgeDocuments.length} 个`);
      }
      
      if (data.userSettings) {
        console.log('   - 用户设置: 已同步');
      }
    } else {
      console.log('❌ 数据同步下载失败:', downloadResponse.data.message);
    }

    console.log('');
    console.log('🎉 分批数据同步功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   请求URL:', error.config?.url);
    }
  }
}

// 运行测试
if (require.main === module) {
  testBatchSync().catch(console.error);
}

module.exports = { testBatchSync };
