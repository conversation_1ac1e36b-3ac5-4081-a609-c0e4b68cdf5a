const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 实时监控上传过程
async function monitorUpload() {
  console.log('=== 实时监控小说上传过程 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  let previousData = null;
  let checkCount = 0;
  let uploadDetected = false;
  
  console.log('🔄 开始监控云端数据变化...');
  console.log('💡 请在Flutter应用中进行数据同步');
  console.log('');

  // 每3秒检查一次云端数据
  const interval = setInterval(async () => {
    try {
      checkCount++;
      const timestamp = new Date().toLocaleTimeString();
      
      const response = await axios.get(`${BASE_URL}/sync/download`, {
        headers: {
          'Authorization': `Bearer ${vipToken}`
        }
      });

      if (response.data.success) {
        const currentData = response.data.data;
        const currentTimestamp = response.data.timestamp;
        
        // 计算数据变化
        const novelCount = currentData.novels ? currentData.novels.length : 0;
        const cardCount = currentData.characterCards ? currentData.characterCards.length : 0;
        const typeCount = currentData.characterTypes ? currentData.characterTypes.length : 0;
        const docCount = currentData.knowledgeDocuments ? currentData.knowledgeDocuments.length : 0;
        const styleCount = currentData.stylePackages ? currentData.stylePackages.length : 0;
        
        console.log(`[${timestamp}] 检查 #${checkCount}`);
        console.log(`   云端时间戳: ${currentTimestamp}`);
        console.log(`   小说: ${novelCount} 本`);
        console.log(`   角色卡片: ${cardCount} 个`);
        console.log(`   角色类型: ${typeCount} 个`);
        console.log(`   知识库文档: ${docCount} 个`);
        console.log(`   风格包: ${styleCount} 个`);
        
        // 检查是否有变化
        if (previousData) {
          const prevNovelCount = previousData.novels ? previousData.novels.length : 0;
          const prevCardCount = previousData.characterCards ? previousData.characterCards.length : 0;
          const prevTypeCount = previousData.characterTypes ? previousData.characterTypes.length : 0;
          const prevDocCount = previousData.knowledgeDocuments ? previousData.knowledgeDocuments.length : 0;
          const prevStyleCount = previousData.stylePackages ? previousData.stylePackages.length : 0;
          
          if (novelCount !== prevNovelCount || 
              cardCount !== prevCardCount || 
              typeCount !== prevTypeCount || 
              docCount !== prevDocCount || 
              styleCount !== prevStyleCount) {
            
            console.log('\n🎉 检测到数据变化!');
            console.log(`   小说: ${prevNovelCount} → ${novelCount}`);
            console.log(`   角色卡片: ${prevCardCount} → ${cardCount}`);
            console.log(`   角色类型: ${prevTypeCount} → ${typeCount}`);
            console.log(`   知识库文档: ${prevDocCount} → ${docCount}`);
            console.log(`   风格包: ${prevStyleCount} → ${styleCount}`);
            
            uploadDetected = true;
            
            // 如果有新的小说数据，显示详情
            if (novelCount > 0 && currentData.novels) {
              console.log('\n📚 小说详情:');
              currentData.novels.forEach((novel, index) => {
                console.log(`   ${index + 1}. "${novel.title}" by ${novel.author || '未知作者'}`);
                console.log(`      ID: ${novel.id}`);
                console.log(`      创建时间: ${novel.createdAt}`);
                console.log(`      内容长度: ${novel.content ? novel.content.length : 0} 字符`);
                console.log(`      章节数: ${novel.chapters ? novel.chapters.length : 0}`);
              });
              
              // 检查是否是用户的真实数据
              const userNovels = currentData.novels.filter(novel => 
                novel.title && (
                  novel.title.includes('赛博朋克') || 
                  novel.title.includes('大秦') || 
                  novel.title.includes('神豪系统')
                )
              );
              
              if (userNovels.length > 0) {
                console.log('\n✅ 发现用户真实小说数据!');
                userNovels.forEach(novel => {
                  console.log(`   ✓ ${novel.title}`);
                });
                
                if (userNovels.length >= 19) {
                  console.log('\n🎊 成功！所有19本小说都已上传！');
                  clearInterval(interval);
                  return;
                }
              } else {
                console.log('\n⚠️ 这些不是用户的真实小说数据');
              }
            }
            
            console.log('');
          } else {
            console.log('   (无变化)');
          }
        }
        
        previousData = currentData;
        
      } else {
        console.log(`[${timestamp}] ❌ 数据获取失败: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log(`[${new Date().toLocaleTimeString()}] ❌ 监控错误: ${error.message}`);
    }
  }, 3000); // 每3秒检查一次
  
  // 2分钟后自动停止
  setTimeout(() => {
    console.log('\n⏰ 监控时间结束');
    if (!uploadDetected) {
      console.log('❌ 未检测到任何数据上传');
      console.log('');
      console.log('🔍 可能的问题:');
      console.log('1. Flutter应用没有开始同步');
      console.log('2. 数据收集阶段失败');
      console.log('3. 网络连接问题');
      console.log('4. 服务器拒绝请求');
    }
    clearInterval(interval);
  }, 120000);
}

// 测试服务器连接
async function testConnection() {
  console.log('=== 测试服务器连接 ===');
  
  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  try {
    console.log('🔗 测试下载接口...');
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });
    
    if (downloadResponse.data.success) {
      console.log('✅ 下载接口正常');
      console.log(`   响应时间: ${downloadResponse.headers['x-response-time'] || '未知'}`);
    } else {
      console.log('❌ 下载接口异常:', downloadResponse.data.message);
    }
    
    console.log('🔗 测试上传接口...');
    const testUploadData = {
      dataType: 'test',
      data: { test: 'connection' },
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1,
      },
      timestamp: new Date().toISOString()
    };
    
    const uploadResponse = await axios.post(`${BASE_URL}/sync/upload`, testUploadData, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (uploadResponse.data.success) {
      console.log('✅ 上传接口正常');
    } else {
      console.log('❌ 上传接口异常:', uploadResponse.data.message);
    }
    
  } catch (error) {
    console.log('❌ 连接测试失败:', error.message);
    if (error.response) {
      console.log('   状态码:', error.response.status);
      console.log('   错误详情:', error.response.data);
    }
  }
}

// 检查命令行参数
if (process.argv.includes('test')) {
  testConnection().catch(console.error);
} else {
  console.log('小说上传实时监控工具');
  console.log('');
  console.log('使用方法:');
  console.log('  node monitor-upload.js        - 开始实时监控');
  console.log('  node monitor-upload.js test   - 测试服务器连接');
  console.log('');
  
  if (process.argv.length === 2) {
    monitorUpload().catch(console.error);
  }
}

module.exports = { monitorUpload, testConnection };
