#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 数据库文件路径
const DB_PATH = path.join(__dirname, 'db.json');

// 读取数据库
function loadDatabase() {
  try {
    const data = fs.readFileSync(DB_PATH, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('读取数据库失败:', error.message);
    process.exit(1);
  }
}

// 保存数据库
function saveDatabase(db) {
  try {
    fs.writeFileSync(DB_PATH, JSON.stringify(db, null, 2), 'utf8');
    console.log('数据库保存成功');
  } catch (error) {
    console.error('保存数据库失败:', error.message);
    process.exit(1);
  }
}

// 生成会员码
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 检查会员码是否存在
function codeExists(db, code) {
  return db.memberCodes.some(mc => mc.code === code);
}

// 验证套餐ID
function validatePackageId(db, packageId) {
  return db.packages.some(pkg => pkg.id === packageId);
}

// 创建单个会员码
function createMemberCode(options = {}) {
  const db = loadDatabase();
  
  const {
    packageId = 'pkg_permanent',
    customCode = null,
    expireAt = null,
    batchId = `batch_${Date.now()}`,
    prefix = 'VIP'
  } = options;
  
  // 验证套餐ID
  if (!validatePackageId(db, packageId)) {
    console.error(`错误: 套餐ID "${packageId}" 不存在`);
    console.log('可用的套餐ID:');
    db.packages.forEach(pkg => {
      console.log(`  - ${pkg.id}: ${pkg.name}`);
    });
    return;
  }
  
  // 生成或使用自定义会员码
  let code = customCode;
  if (!code) {
    let attempts = 0;
    do {
      code = generateMemberCode(prefix);
      attempts++;
      if (attempts > 100) {
        console.error('生成唯一会员码失败，请重试');
        return;
      }
    } while (codeExists(db, code));
  } else if (codeExists(db, code)) {
    console.error(`错误: 会员码 "${code}" 已存在`);
    return;
  }
  
  const newMemberCode = {
    code,
    packageId,
    isUsed: false,
    usedBy: null,
    usedAt: null,
    expireAt: expireAt,
    batchId: batchId,
    createdAt: new Date().toISOString()
  };
  
  db.memberCodes.push(newMemberCode);
  saveDatabase(db);
  
  console.log('✅ 会员码创建成功:');
  console.log(`   会员码: ${code}`);
  console.log(`   套餐: ${packageId}`);
  console.log(`   批次: ${batchId}`);
  if (expireAt) {
    console.log(`   过期时间: ${expireAt}`);
  }
  
  return newMemberCode;
}

// 批量创建会员码
function batchCreateMemberCodes(options = {}) {
  const db = loadDatabase();
  
  const {
    packageId = 'pkg_permanent',
    count = 10,
    expireAt = null,
    batchId = `batch_${Date.now()}`,
    prefix = 'VIP'
  } = options;
  
  // 验证套餐ID
  if (!validatePackageId(db, packageId)) {
    console.error(`错误: 套餐ID "${packageId}" 不存在`);
    return;
  }
  
  const createdCodes = [];
  
  console.log(`开始批量创建 ${count} 个会员码...`);
  
  for (let i = 0; i < count; i++) {
    let code;
    let attempts = 0;
    
    do {
      code = generateMemberCode(prefix);
      attempts++;
      if (attempts > 100) {
        console.error(`第 ${i + 1} 个会员码生成失败`);
        break;
      }
    } while (codeExists(db, code));
    
    if (attempts <= 100) {
      const newMemberCode = {
        code,
        packageId,
        isUsed: false,
        usedBy: null,
        usedAt: null,
        expireAt: expireAt,
        batchId: batchId,
        createdAt: new Date().toISOString()
      };
      
      db.memberCodes.push(newMemberCode);
      createdCodes.push(newMemberCode);
    }
  }
  
  saveDatabase(db);
  
  console.log(`✅ 成功创建 ${createdCodes.length} 个会员码:`);
  console.log(`   批次ID: ${batchId}`);
  console.log(`   套餐: ${packageId}`);
  if (expireAt) {
    console.log(`   过期时间: ${expireAt}`);
  }
  console.log('\n会员码列表:');
  createdCodes.forEach((code, index) => {
    console.log(`   ${index + 1}. ${code.code}`);
  });
  
  return createdCodes;
}

// 列出会员码
function listMemberCodes(options = {}) {
  const db = loadDatabase();
  
  const {
    isUsed = null,
    packageId = null,
    batchId = null,
    limit = 20
  } = options;
  
  let memberCodes = db.memberCodes;
  
  // 过滤
  if (isUsed !== null) {
    memberCodes = memberCodes.filter(code => code.isUsed === isUsed);
  }
  
  if (packageId) {
    memberCodes = memberCodes.filter(code => code.packageId === packageId);
  }
  
  if (batchId) {
    memberCodes = memberCodes.filter(code => code.batchId === batchId);
  }
  
  // 限制数量
  if (limit > 0) {
    memberCodes = memberCodes.slice(0, limit);
  }
  
  console.log(`找到 ${memberCodes.length} 个会员码:`);
  console.log('');
  
  memberCodes.forEach((code, index) => {
    const status = code.isUsed ? '已使用' : '未使用';
    const expireInfo = code.expireAt ? ` (过期: ${code.expireAt})` : '';
    const usedInfo = code.isUsed ? ` (使用者: ${code.usedBy}, 使用时间: ${code.usedAt})` : '';
    
    console.log(`${index + 1}. ${code.code} - ${status}${expireInfo}`);
    console.log(`   套餐: ${code.packageId} | 批次: ${code.batchId}${usedInfo}`);
    console.log('');
  });
}

// 获取统计信息
function getStats() {
  const db = loadDatabase();
  const memberCodes = db.memberCodes;
  
  const stats = {
    total: memberCodes.length,
    used: memberCodes.filter(code => code.isUsed).length,
    unused: memberCodes.filter(code => !code.isUsed).length,
    expired: memberCodes.filter(code => 
      code.expireAt && new Date(code.expireAt) < new Date()
    ).length
  };
  
  console.log('📊 会员码统计信息:');
  console.log(`   总数: ${stats.total}`);
  console.log(`   已使用: ${stats.used}`);
  console.log(`   未使用: ${stats.unused}`);
  console.log(`   已过期: ${stats.expired}`);
  
  // 按套餐统计
  const byPackage = {};
  memberCodes.forEach(code => {
    if (!byPackage[code.packageId]) {
      byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
    }
    byPackage[code.packageId].total++;
    if (code.isUsed) {
      byPackage[code.packageId].used++;
    } else {
      byPackage[code.packageId].unused++;
    }
  });
  
  console.log('\n按套餐统计:');
  Object.entries(byPackage).forEach(([packageId, stats]) => {
    console.log(`   ${packageId}: 总数 ${stats.total}, 已使用 ${stats.used}, 未使用 ${stats.unused}`);
  });
}

// 命令行处理
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      const createOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          createOptions[key] = value;
        }
      }
      createMemberCode(createOptions);
      break;
      
    case 'batch':
      const batchOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          if (key === 'count') {
            batchOptions[key] = parseInt(value);
          } else {
            batchOptions[key] = value;
          }
        }
      }
      batchCreateMemberCodes(batchOptions);
      break;
      
    case 'list':
      const listOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          if (key === 'isUsed') {
            listOptions[key] = value === 'true';
          } else if (key === 'limit') {
            listOptions[key] = parseInt(value);
          } else {
            listOptions[key] = value;
          }
        }
      }
      listMemberCodes(listOptions);
      break;
      
    case 'stats':
      getStats();
      break;
      
    default:
      console.log('会员码管理工具');
      console.log('');
      console.log('用法:');
      console.log('  node member-code-manager.js create [选项]     - 创建单个会员码');
      console.log('  node member-code-manager.js batch [选项]      - 批量创建会员码');
      console.log('  node member-code-manager.js list [选项]       - 列出会员码');
      console.log('  node member-code-manager.js stats            - 显示统计信息');
      console.log('');
      console.log('创建选项:');
      console.log('  --packageId     套餐ID (pkg_monthly 或 pkg_permanent)');
      console.log('  --customCode    自定义会员码');
      console.log('  --expireAt      过期时间 (ISO格式)');
      console.log('  --batchId       批次ID');
      console.log('  --prefix        会员码前缀 (默认: VIP)');
      console.log('');
      console.log('批量创建选项:');
      console.log('  --count         创建数量 (默认: 10)');
      console.log('  --packageId     套餐ID');
      console.log('  --expireAt      过期时间');
      console.log('  --batchId       批次ID');
      console.log('  --prefix        会员码前缀');
      console.log('');
      console.log('列表选项:');
      console.log('  --isUsed        过滤使用状态 (true/false)');
      console.log('  --packageId     过滤套餐ID');
      console.log('  --batchId       过滤批次ID');
      console.log('  --limit         限制数量 (默认: 20)');
      console.log('');
      console.log('示例:');
      console.log('  node member-code-manager.js create --packageId pkg_permanent --customCode MYVIP001');
      console.log('  node member-code-manager.js batch --count 50 --packageId pkg_monthly --prefix MONTH');
      console.log('  node member-code-manager.js list --isUsed false --limit 10');
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  createMemberCode,
  batchCreateMemberCodes,
  listMemberCodes,
  getStats
};
