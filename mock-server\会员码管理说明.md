# 会员码管理系统使用说明

## 概述

会员码管理系统已经完全集成到腾讯云数据库中，支持创建、查询、验证和管理会员码。会员码存储在CloudBase数据库的 `memberData` 集合中。

## 数据结构

会员码在数据库中的数据结构：

```json
{
  "_id": "数据库自动生成的ID",
  "code": "会员码字符串",
  "packageId": "套餐ID (pkg_monthly 或 pkg_permanent)",
  "isUsed": false,
  "usedBy": "使用者用户ID (使用后填入)",
  "usedAt": "使用时间 (使用后填入)",
  "expireAt": "过期时间 (可选)",
  "batchId": "批次ID",
  "createdAt": "创建时间"
}
```

## 可用的套餐类型

- `pkg_monthly`: 月度会员 (30天)
- `pkg_permanent`: 永久会员 (终身)

## 管理工具

### 1. 腾讯云会员码管理工具 (推荐)

使用 `cloudbase-member-code-manager.js` 直接操作腾讯云数据库：

#### 交互式模式
```bash
node cloudbase-member-code-manager.js
```

#### 命令行模式

**创建单个会员码：**
```bash
# 创建永久会员码
node cloudbase-member-code-manager.js create --packageId pkg_permanent --customCode MYVIP001

# 创建月度会员码，带过期时间
node cloudbase-member-code-manager.js create --packageId pkg_monthly --expireAt "2025-12-31T23:59:59.000Z"

# 批量创建50个月度会员码
node cloudbase-member-code-manager.js batch --count 50 --packageId pkg_monthly --prefix MONTH
```

**查看会员码：**
```bash
# 查看所有未使用的会员码
node cloudbase-member-code-manager.js list --isUsed false

# 查看永久会员码
node cloudbase-member-code-manager.js list --packageId pkg_permanent

# 查看统计信息
node cloudbase-member-code-manager.js stats
```

**删除会员码：**
```bash
node cloudbase-member-code-manager.js delete MYVIP001
```

### 2. 本地文件管理工具 (备用)

使用 `member-code-manager.js` 操作本地 `db.json` 文件：

```bash
# 创建会员码
node member-code-manager.js create --packageId pkg_permanent --customCode LOCAL001

# 批量创建
node member-code-manager.js batch --count 10 --packageId pkg_monthly
```

## API接口

### 用户端接口

1. **验证会员码**
   - 接口: `POST /member-code/validate`
   - 参数: `{ "code": "会员码" }`

### 管理端接口

1. **创建单个会员码**
   - 接口: `POST /admin/member-code/create`
   - 参数: 
     ```json
     {
       "packageId": "pkg_permanent",
       "customCode": "MYVIP001",
       "expireAt": "2025-12-31T23:59:59.000Z",
       "batchId": "batch_001",
       "prefix": "VIP"
     }
     ```

2. **批量创建会员码**
   - 接口: `POST /admin/member-code/batch-create`
   - 参数:
     ```json
     {
       "packageId": "pkg_permanent",
       "count": 50,
       "expireAt": "2025-12-31T23:59:59.000Z",
       "batchId": "batch_001",
       "prefix": "VIP"
     }
     ```

3. **获取会员码列表**
   - 接口: `GET /admin/member-code/list`
   - 参数: `?page=1&limit=20&isUsed=false&packageId=pkg_permanent&batchId=batch_001`

4. **获取统计信息**
   - 接口: `GET /admin/member-code/stats`

5. **删除会员码**
   - 接口: `DELETE /admin/member-code/{code}`

## 快速开始

### 1. 设置环境变量

确保设置了正确的腾讯云环境ID：

```bash
export TCB_ENV=your-cloudbase-env-id
```

或者在代码中修改默认值。

### 2. 创建一些测试会员码

```bash
# 进入交互式模式
node cloudbase-member-code-manager.js

# 选择 "2. 批量创建会员码"
# 输入创建数量: 10
# 套餐ID: pkg_permanent
# 其他选项可以留空使用默认值
```

### 3. 查看创建的会员码

```bash
# 查看统计信息
node cloudbase-member-code-manager.js stats

# 查看未使用的会员码
node cloudbase-member-code-manager.js list --isUsed false --limit 10
```

### 4. 测试会员码验证

使用任何HTTP客户端测试验证接口：

```bash
curl -X POST https://your-env-id.service.tcloudbase.com/member-code/validate \
  -H "Content-Type: application/json" \
  -d '{"code":"刚创建的会员码"}'
```

## 注意事项

1. **数据库权限**: 确保CloudBase数据库的 `memberData` 集合有正确的读写权限。

2. **会员码格式**: 默认格式为 `前缀 + 8位随机字符`，例如 `VIP12345678`。

3. **批量创建限制**: 为了避免超时，批量创建限制最多100个会员码。

4. **已使用的会员码**: 已使用的会员码不能删除，只能查看。

5. **过期时间**: 如果设置了过期时间，验证时会检查是否过期。

6. **批次管理**: 使用批次ID可以方便地管理和查询同一批创建的会员码。

## 故障排除

### 数据库连接失败
- 检查 `TCB_ENV` 环境变量是否正确
- 确认CloudBase环境是否正常运行
- 检查网络连接

### 会员码创建失败
- 检查套餐ID是否正确 (`pkg_monthly` 或 `pkg_permanent`)
- 确认自定义会员码没有重复
- 检查过期时间格式是否正确 (ISO 8601格式)

### API调用失败
- 确认云函数已正确部署
- 检查API路径是否正确
- 查看云函数日志获取详细错误信息

## 示例场景

### 场景1: 为新用户准备欢迎礼包
```bash
# 创建100个永久会员码作为新用户礼包
node cloudbase-member-code-manager.js batch \
  --count 100 \
  --packageId pkg_permanent \
  --prefix WELCOME \
  --batchId welcome_2025
```

### 场景2: 限时活动会员码
```bash
# 创建50个月度会员码，设置过期时间
node cloudbase-member-code-manager.js batch \
  --count 50 \
  --packageId pkg_monthly \
  --prefix PROMO \
  --expireAt "2025-12-31T23:59:59.000Z" \
  --batchId promo_2025
```

### 场景3: 查看活动效果
```bash
# 查看特定批次的使用情况
node cloudbase-member-code-manager.js list --batchId welcome_2025

# 查看整体统计
node cloudbase-member-code-manager.js stats
```

这样，您就可以完全通过腾讯云数据库来管理会员码，而不需要依赖本地文件了。
