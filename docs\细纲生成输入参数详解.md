# 细纲生成输入参数详解

## 概述

经过连贯性保证机制的修复和完善，现在细纲生成会接收以下完整的上下文参数，确保AI能够充分理解故事的整体发展脉络和历史上下文。

## 完整输入参数列表

### 1. 基础小说信息参数
```dart
{
  'novelTitle': '小说标题',                    // 当前小说的名称
  'genres': '玄幻, 都市',                      // 小说类型（逗号分隔）
  'theme': '成长与复仇',                       // 小说主题
  'targetReaders': '年轻读者',                 // 目标读者群体
  'background': '古代修仙世界',                // 故事背景设定
  'otherRequirements': '注重爽点设计',         // 其他特殊要求
}
```

### 2. 当前章节信息参数
```dart
{
  'chapterNumber': '4',                        // 当前章节编号（字符串）
  'chapterTitle': '帝师出笼，公子请安',         // 当前章节标题
  'chapterSummary': '昔日阶下囚，今朝帝王师...',// 当前章节概要（来自基础大纲）
}
```

### 3. 🆕 完整故事大纲参数（去重版）
```dart
{
  'fullOutline': '''
=== 完整故事发展脉络 ===
小说标题：某某小说
计划总章节数：50

### 章节发展脉络：
**第5章：标题5**
概要：第5章的情节概要...（尚未生成详细细纲）

**第6章：标题6**
概要：第6章的情节概要...（尚未生成详细细纲）

**第7章：标题7**
概要：第7章的情节概要...（尚未生成详细细纲）
...
=== 大纲结束 ===

注意：已生成详细细纲的章节（如第1-4章）不会在此处重复显示，
它们的详细信息在"历史上下文"部分提供。
'''
}
```

### 4. 角色和风格参数
```dart
{
  'characters': '''
# 角色设定
## 扶苏
- 性别: 男
- 年龄: 25
- 外貌: 温文尔雅，气质出众
- 性格: 仁厚有余，决断不足
- 背景: 秦始皇长子
- 技能: 儒学修养深厚
- 关系: 与蒙恬亦师亦友
- 其他: 被贬九原郡

## 赵惊
- 性别: 男
- 年龄: 30
- 外貌: 普通，但眼神锐利
- 性格: 狡黠多谋，敢于冒险
- 背景: 神秘来历
- 技能: 政治谋略，帝王心术
- 关系: 扶苏的老师
- 其他: 曾被下狱
''',
  'writingStylePrompt': '采用第三人称限制视角，注重心理描写...',
  'knowledgeBase': '秦朝历史背景知识...',
}
```

### 5. ~~上下文连贯性参数~~（已移除，避免重复）
```dart
// 这些参数已被移除，因为信息已包含在 history 和 fullOutline 中：
// - previousChapterInfo: 前一章信息已在 history 中提供
// - nextChapterInfo: 后续章节信息已在 fullOutline 中提供
```

### 6. 🆕 历史上下文参数（通过Memory机制自动注入）
```dart
{
  'history': '''
=== 已生成的章节细纲和关键事件时间线 ===
**重要提醒：严格禁止重复以下已发生的情节和事件！**

### 关键事件时间线（已发生）：
- 第1章：赵惊被捕入狱，因妖言惑众罪名
- 第2章：扶苏接到父皇密令，震惊不已
- 第3章：赵惊被请出大牢，成为扶苏之师

### 第1章：某某标题（前文章节 - 已发生）
**状态：已完成，不可重复**
[完整的第1章细纲内容]
---

### 第2章：某某标题（前文章节 - 已发生）
**状态：已完成，不可重复**
[完整的第2章细纲内容]
---

### 第5章：某某标题（后续章节 - 计划中）
**状态：尚未发生，可作参考**
[第5章的细纲内容]
---
'''
}
```

## 参数构建逻辑

### 完整大纲构建
```dart
String _buildFullOutlineString(Map<String, dynamic> fullOutline) {
  // 1. 提取小说基本信息
  // 2. 按章节号排序所有章节
  // 3. 格式化输出每章的标题和概要
  // 4. 构建易读的发展脉络
}
```

### 历史上下文构建
```dart
// 1. 获取所有已生成的详细细纲
final allChapters = await novelMemory.getAllChapters();

// 2. 构建关键事件时间线
// 3. 区分前文章节（已发生）和后续章节（计划中）
// 4. 明确标记状态，防止重复
```

### 前后章节信息构建
```dart
// 优先使用已生成的详细细纲
if (allDetailedChapters.containsKey(chapterNumber - 1)) {
  // 使用详细细纲
} else {
  // 使用基础大纲
}
```

## 提示词模板中的使用

```dart
'''
**完整故事大纲（整体发展脉络）:**
{fullOutline}

**历史上下文和已生成细纲:**
{history}

**连贯性要求（重要）:**
*   **理解整体脉络**：仔细阅读完整故事大纲，理解本章在整个故事中的位置和作用
*   **严格禁止重复已发生的情节**
*   **确保时间线连贯**
*   **保持角色状态一致**
*   **情节自然衔接**
*   **服务整体发展**：本章的细纲应该推进整体故事发展，为后续章节做好铺垫
'''
```

## 关键改进点

1. **🆕 完整故事脉络**：AI现在能看到整个故事的发展规划
2. **🆕 关键事件时间线**：明确列出已发生的重要事件
3. **🆕 状态标记**：清楚区分已完成和计划中的章节
4. **🆕 详细历史上下文**：使用完整细纲而不是简要概述
5. **🆕 连贯性要求强化**：在提示词中明确要求避免重复和保持连贯
6. **🔧 信息去重优化**：避免在不同参数中重复相同的章节信息

## 预期效果

- ✅ **避免重复情节**：不会再出现多次相同的场景和事件
- ✅ **保持时间线连贯**：每章都能在前章基础上自然发展
- ✅ **角色状态一致**：角色的位置、关系、状态保持连续
- ✅ **情节自然衔接**：新章节与前章无缝连接
- ✅ **服务整体发展**：每章都推进整体故事进程
- ✅ **理解故事全貌**：AI能够把握整个故事的发展方向
