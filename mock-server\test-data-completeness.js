const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试数据完整性
async function testDataCompleteness() {
  console.log('=== 数据完整性测试 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('📥 下载当前云端数据...');
    
    // 下载数据
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据下载成功:', downloadResponse.data.message);
      console.log('   时间戳:', downloadResponse.data.timestamp);
      
      const data = downloadResponse.data.data;
      console.log('');
      console.log('📊 云端数据详情:');
      
      // 检查各种数据类型
      const dataTypes = [
        { key: 'novels', name: '小说' },
        { key: 'characterCards', name: '角色卡片' },
        { key: 'characterTypes', name: '角色类型' },
        { key: 'knowledgeDocuments', name: '知识库文档' },
        { key: 'stylePackages', name: '风格包' },
        { key: 'userSettings', name: '用户设置' }
      ];

      let hasAnyData = false;
      
      dataTypes.forEach(type => {
        if (data[type.key]) {
          if (Array.isArray(data[type.key])) {
            const count = data[type.key].length;
            if (count > 0) {
              console.log(`✅ ${type.name}: ${count} 个`);
              hasAnyData = true;
              
              // 显示前几个项目的详情
              if (count > 0) {
                console.log(`   前几个${type.name}:`);
                for (let i = 0; i < Math.min(3, count); i++) {
                  const item = data[type.key][i];
                  if (item.title) {
                    console.log(`   ${i + 1}. ${item.title} (${item.id})`);
                  } else if (item.name) {
                    console.log(`   ${i + 1}. ${item.name} (${item.id})`);
                  } else {
                    console.log(`   ${i + 1}. ${item.id}`);
                  }
                }
              }
            } else {
              console.log(`❌ ${type.name}: 0 个`);
            }
          } else {
            console.log(`✅ ${type.name}: 存在`);
            hasAnyData = true;
            if (type.key === 'userSettings') {
              console.log(`   设置内容: ${JSON.stringify(data[type.key]).substring(0, 100)}...`);
            }
          }
        } else {
          console.log(`❌ ${type.name}: 不存在`);
        }
      });

      console.log('');
      if (hasAnyData) {
        console.log('🎯 分析结果: 云端有数据，但可能某些类型缺失');
        console.log('');
        console.log('🔍 可能的问题:');
        console.log('1. 数据上传时某些类型失败了');
        console.log('2. 数据收集时某些控制器没有数据');
        console.log('3. 数据应用时某些类型没有正确处理');
      } else {
        console.log('❌ 云端没有任何数据，可能是首次同步失败');
      }

    } else {
      console.log('❌ 数据下载失败:', downloadResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
    }
  }
}

// 测试上传各种数据类型
async function testUploadDataTypes() {
  console.log('\n=== 测试上传各种数据类型 ===');
  
  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  const testData = {
    novels: [
      {
        id: 'test_novel_001',
        title: '测试小说1',
        author: '测试作者',
        content: '测试内容',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    characterCards: [
      {
        id: 'test_card_001',
        name: '测试角色卡片',
        description: '测试描述',
        novelId: 'test_novel_001'
      }
    ],
    characterTypes: [
      {
        id: 'test_type_001',
        name: '测试角色类型',
        description: '测试类型描述'
      }
    ],
    knowledgeDocuments: [
      {
        id: 'test_doc_001',
        title: '测试知识库文档',
        content: '测试文档内容',
        type: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ],
    stylePackages: [
      {
        id: 'test_style_001',
        name: '测试风格包',
        description: '测试风格描述'
      }
    ],
    userSettings: {
      theme: 'dark',
      autoSync: true,
      enableNotification: true
    }
  };

  // 逐个测试每种数据类型
  for (const [dataType, data] of Object.entries(testData)) {
    console.log(`\n🔄 测试上传 ${dataType}...`);
    
    try {
      const response = await axios.post(`${BASE_URL}/sync/upload`, {
        dataType: dataType,
        data: { [dataType]: data },
        batchInfo: {
          isComplete: true,
          batchId: null,
          batchIndex: 0,
          totalBatches: 1
        },
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Authorization': `Bearer ${vipToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log(`✅ ${dataType} 上传成功`);
      } else {
        console.log(`❌ ${dataType} 上传失败: ${response.data.message}`);
      }
    } catch (error) {
      console.log(`❌ ${dataType} 上传异常: ${error.response?.data?.message || error.message}`);
    }
  }
}

// 运行测试
async function main() {
  await testDataCompleteness();
  await testUploadDataTypes();
  
  console.log('\n🎯 调试建议:');
  console.log('1. 检查Flutter应用的控制台日志，看数据收集阶段的详细信息');
  console.log('2. 确认NovelController和CharacterCardController是否正确初始化');
  console.log('3. 验证数据上传时是否所有批次都成功');
  console.log('4. 检查数据下载后的应用逻辑是否正确执行');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testDataCompleteness, testUploadDataTypes };
