const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 模拟Flutter应用的实际请求
async function testFlutterRequest() {
  console.log('=== 模拟Flutter应用请求测试 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('🔄 测试Flutter格式的数据上传...');
    
    // 模拟Flutter应用发送的压缩数据格式
    const flutterData = {
      dataType: 'novels',
      data: {
        novels: [
          {
            id: 'flutter_test_001',
            title: 'Flutter测试小说',
            author: '测试作者',
            content: '这是Flutter应用发送的测试内容...',
            chapters: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      },
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1
      },
      timestamp: new Date().toISOString()
    };

    // 计算数据大小
    const dataString = JSON.stringify(flutterData);
    const dataSizeBytes = Buffer.byteLength(dataString, 'utf8');
    console.log(`📊 请求数据大小: ${dataSizeBytes} 字节 (${(dataSizeBytes/1024).toFixed(2)} KB)`);

    // 发送请求
    const response = await axios.post(`${BASE_URL}/sync/upload`, flutterData, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ Flutter格式数据上传成功:', response.data.message);
      console.log('   时间戳:', response.data.timestamp);
      console.log('   批次信息:', response.data.batchInfo);
    } else {
      console.log('❌ Flutter格式数据上传失败:', response.data.message);
    }

    console.log('');
    console.log('🔄 测试分批上传...');

    // 测试分批上传
    const batchData = {
      dataType: 'novels',
      data: {
        novels: [
          {
            id: 'batch_test_001',
            title: '分批测试小说1',
            author: '测试作者',
            content: '分批上传测试内容1',
            chapters: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: 'batch_test_002',
            title: '分批测试小说2',
            author: '测试作者',
            content: '分批上传测试内容2',
            chapters: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      },
      batchInfo: {
        isComplete: false,
        batchId: 'batch_' + Date.now(),
        batchIndex: 0,
        totalBatches: 2
      },
      timestamp: new Date().toISOString()
    };

    const batchResponse = await axios.post(`${BASE_URL}/sync/upload`, batchData, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (batchResponse.data.success) {
      console.log('✅ 分批上传成功:', batchResponse.data.message);
    } else {
      console.log('❌ 分批上传失败:', batchResponse.data.message);
    }

    console.log('');
    console.log('📥 测试数据下载...');

    // 测试下载
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据下载成功:', downloadResponse.data.message);
      const data = downloadResponse.data.data;
      if (data.novels && data.novels.length > 0) {
        console.log(`   下载到 ${data.novels.length} 本小说:`);
        data.novels.forEach((novel, index) => {
          console.log(`   ${index + 1}. ${novel.title} (${novel.id})`);
        });
      }
    } else {
      console.log('❌ 数据下载失败:', downloadResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
      
      // 如果是500错误，提供调试建议
      if (error.response.status === 500) {
        console.error('');
        console.error('🔍 500错误调试建议:');
        console.error('1. 检查云函数日志中的详细错误信息');
        console.error('2. 验证请求数据格式是否正确');
        console.error('3. 检查数据库操作是否成功');
        console.error('4. 确认用户权限和Token有效性');
      }
    }
  }
}

// 测试错误情况
async function testErrorCases() {
  console.log('\n=== 错误情况测试 ===');
  
  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  // 测试无效数据类型
  console.log('🔄 测试无效数据类型...');
  try {
    await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'invalid_type',
      data: { test: 'data' },
      batchInfo: { isComplete: true, batchIndex: 0, totalBatches: 1 },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('❌ 意外：无效数据类型请求成功了');
  } catch (error) {
    if (error.response && error.response.status === 500) {
      console.log('✅ 正确：无效数据类型被拒绝 (500)');
    } else {
      console.log('❌ 无效数据类型测试异常:', error.message);
    }
  }

  // 测试空请求体
  console.log('🔄 测试空请求体...');
  try {
    await axios.post(`${BASE_URL}/sync/upload`, null, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('❌ 意外：空请求体成功了');
  } catch (error) {
    if (error.response && error.response.status === 500) {
      console.log('✅ 正确：空请求体被拒绝 (500)');
    } else {
      console.log('❌ 空请求体测试异常:', error.message);
    }
  }
}

// 运行测试
async function main() {
  await testFlutterRequest();
  await testErrorCases();
  
  console.log('\n🎯 总结:');
  console.log('如果所有测试都通过，说明服务器端500错误已修复');
  console.log('如果仍有500错误，请检查云函数日志获取详细错误信息');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testFlutterRequest, testErrorCases };
