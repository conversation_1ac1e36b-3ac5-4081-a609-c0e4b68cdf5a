const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 监控数据同步过程
async function monitorSync() {
  console.log('=== 数据同步实时监控 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  let previousData = null;
  let checkCount = 0;
  
  console.log('🔄 开始监控云端数据变化...');
  console.log('💡 请在另一个窗口进行Flutter应用的数据同步');
  console.log('');

  // 每5秒检查一次云端数据
  const interval = setInterval(async () => {
    try {
      checkCount++;
      console.log(`\n📊 检查 #${checkCount} (${new Date().toLocaleTimeString()})`);
      
      const response = await axios.get(`${BASE_URL}/sync/download`, {
        headers: {
          'Authorization': `Bearer ${vipToken}`
        }
      });

      if (response.data.success) {
        const currentData = response.data.data;
        const timestamp = response.data.timestamp;
        
        // 计算数据变化
        const novelCount = currentData.novels ? currentData.novels.length : 0;
        const cardCount = currentData.characterCards ? currentData.characterCards.length : 0;
        const typeCount = currentData.characterTypes ? currentData.characterTypes.length : 0;
        const docCount = currentData.knowledgeDocuments ? currentData.knowledgeDocuments.length : 0;
        const styleCount = currentData.stylePackages ? currentData.stylePackages.length : 0;
        
        console.log(`   时间戳: ${timestamp}`);
        console.log(`   小说: ${novelCount} 本`);
        console.log(`   角色卡片: ${cardCount} 个`);
        console.log(`   角色类型: ${typeCount} 个`);
        console.log(`   知识库文档: ${docCount} 个`);
        console.log(`   风格包: ${styleCount} 个`);
        
        // 检查是否有变化
        if (previousData) {
          const prevNovelCount = previousData.novels ? previousData.novels.length : 0;
          const prevCardCount = previousData.characterCards ? previousData.characterCards.length : 0;
          const prevTypeCount = previousData.characterTypes ? previousData.characterTypes.length : 0;
          const prevDocCount = previousData.knowledgeDocuments ? previousData.knowledgeDocuments.length : 0;
          const prevStyleCount = previousData.stylePackages ? previousData.stylePackages.length : 0;
          
          if (novelCount !== prevNovelCount || 
              cardCount !== prevCardCount || 
              typeCount !== prevTypeCount || 
              docCount !== prevDocCount || 
              styleCount !== prevStyleCount) {
            
            console.log('\n🎉 检测到数据变化!');
            console.log(`   小说: ${prevNovelCount} → ${novelCount}`);
            console.log(`   角色卡片: ${prevCardCount} → ${cardCount}`);
            console.log(`   角色类型: ${prevTypeCount} → ${typeCount}`);
            console.log(`   知识库文档: ${prevDocCount} → ${docCount}`);
            console.log(`   风格包: ${prevStyleCount} → ${styleCount}`);
            
            // 如果有新的小说数据，显示详情
            if (novelCount > 0 && currentData.novels) {
              console.log('\n📚 新的小说数据:');
              currentData.novels.forEach((novel, index) => {
                console.log(`   ${index + 1}. "${novel.title}" by ${novel.author}`);
                console.log(`      ID: ${novel.id}`);
                console.log(`      创建时间: ${novel.createdAt}`);
              });
              
              // 检查是否是用户的真实数据
              const userNovels = currentData.novels.filter(novel => 
                novel.title.includes('赛博朋克') || 
                novel.title.includes('大秦') || 
                novel.title.includes('神豪系统')
              );
              
              if (userNovels.length > 0) {
                console.log('\n✅ 发现用户真实数据!');
                userNovels.forEach(novel => {
                  console.log(`   ✓ ${novel.title}`);
                });
              } else {
                console.log('\n⚠️ 这些不是用户的真实数据');
              }
            }
          } else {
            console.log('   (无变化)');
          }
        }
        
        previousData = currentData;
        
        // 如果检测到用户数据，停止监控
        if (novelCount >= 19) {
          console.log('\n🎯 检测到大量小说数据，可能是用户数据同步成功!');
          console.log('停止监控...');
          clearInterval(interval);
        }
        
      } else {
        console.log('❌ 数据获取失败:', response.data.message);
      }
      
    } catch (error) {
      console.log('❌ 监控错误:', error.message);
    }
  }, 5000); // 每5秒检查一次
  
  // 60秒后自动停止
  setTimeout(() => {
    console.log('\n⏰ 监控时间结束');
    clearInterval(interval);
  }, 60000);
}

// 测试特定Token的数据
async function testTokenData() {
  console.log('=== 测试不同Token的数据 ===');
  
  const tokens = [
    'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==', // 我们使用的Token
  ];
  
  for (const token of tokens) {
    console.log(`\n🔑 测试Token: ${token.substring(0, 20)}...`);
    
    try {
      const response = await axios.get(`${BASE_URL}/sync/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        const data = response.data.data;
        const novelCount = data.novels ? data.novels.length : 0;
        const cardCount = data.characterCards ? data.characterCards.length : 0;
        
        console.log(`   小说: ${novelCount} 本`);
        console.log(`   角色卡片: ${cardCount} 个`);
        console.log(`   时间戳: ${response.data.timestamp}`);
        
        if (novelCount > 0) {
          console.log('   前几本小说:');
          data.novels.slice(0, 3).forEach((novel, index) => {
            console.log(`     ${index + 1}. ${novel.title}`);
          });
        }
      } else {
        console.log('   ❌ 获取失败:', response.data.message);
      }
    } catch (error) {
      console.log('   ❌ 请求失败:', error.response?.status, error.response?.data?.message || error.message);
    }
  }
}

// 检查命令行参数
if (process.argv.includes('monitor')) {
  monitorSync().catch(console.error);
} else if (process.argv.includes('token')) {
  testTokenData().catch(console.error);
} else {
  console.log('数据同步监控工具');
  console.log('');
  console.log('使用方法:');
  console.log('  node monitor-sync.js monitor  - 实时监控数据变化');
  console.log('  node monitor-sync.js token    - 测试Token数据');
  console.log('');
  console.log('💡 建议使用步骤:');
  console.log('1. 运行 "node monitor-sync.js monitor"');
  console.log('2. 在Flutter应用中进行数据同步');
  console.log('3. 观察实时数据变化');
}

module.exports = { monitorSync, testTokenData };
