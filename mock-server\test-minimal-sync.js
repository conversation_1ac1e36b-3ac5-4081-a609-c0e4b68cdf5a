const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试最小数据同步
async function testMinimalSync() {
  console.log('=== 最小数据同步测试 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('🔄 测试最小数据上传...');
    
    // 最小的测试数据
    const minimalData = {
      novels: [{
        id: 'test_001',
        title: '测试小说',
        author: '测试作者',
        content: '这是测试内容',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }]
    };

    // 计算数据大小
    const dataString = JSON.stringify(minimalData);
    const dataSizeBytes = Buffer.byteLength(dataString, 'utf8');
    console.log(`📊 数据大小: ${dataSizeBytes} 字节 (${(dataSizeBytes/1024).toFixed(2)} KB)`);
    console.log(`📝 数据内容: ${dataString}`);

    // 上传数据
    const response = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'novels',
      data: minimalData,
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1,
      },
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ 最小数据上传成功:', response.data.message);
    } else {
      console.log('❌ 最小数据上传失败:', response.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   请求URL:', error.config?.url);
      console.error('   请求头:', error.config?.headers);
      console.error('   请求体大小:', Buffer.byteLength(JSON.stringify(error.config?.data), 'utf8'), '字节');
    }
  }
}

// 测试空数据
async function testEmptySync() {
  console.log('\n🔄 测试空数据上传...');
  
  try {
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    const response = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'userSettings',
      data: { userSettings: { theme: 'dark' } },
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1,
      },
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ 空数据上传成功:', response.data.message);
    } else {
      console.log('❌ 空数据上传失败:', response.data.message);
    }

  } catch (error) {
    console.error('❌ 空数据测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
    }
  }
}

// 运行测试
async function main() {
  await testMinimalSync();
  await testEmptySync();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testMinimalSync, testEmptySync };
