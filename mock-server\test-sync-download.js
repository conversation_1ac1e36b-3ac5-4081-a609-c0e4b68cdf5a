const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试完整的上传下载流程
async function testSyncFlow() {
  console.log('=== 完整数据同步流程测试 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    // 使用VIP用户的Token
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('📤 步骤1: 上传测试数据...');
    
    // 上传一些测试数据
    const testData = {
      novels: [
        {
          id: 'novel_sync_test_001',
          title: '同步测试小说1',
          author: '测试作者',
          content: '这是同步测试的小说内容1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'novel_sync_test_002',
          title: '同步测试小说2',
          author: '测试作者',
          content: '这是同步测试的小说内容2',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]
    };

    // 上传数据
    const uploadResponse = await axios.post(`${BASE_URL}/sync/upload`, {
      dataType: 'novels',
      data: testData,
      batchInfo: {
        isComplete: true,
        batchId: null,
        batchIndex: 0,
        totalBatches: 1,
      },
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (uploadResponse.data.success) {
      console.log('✅ 数据上传成功:', uploadResponse.data.message);
    } else {
      console.log('❌ 数据上传失败:', uploadResponse.data.message);
      return;
    }

    console.log('');
    console.log('📥 步骤2: 下载同步数据...');

    // 下载数据
    const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${vipToken}`
      }
    });

    if (downloadResponse.data.success) {
      console.log('✅ 数据下载成功:', downloadResponse.data.message);
      console.log('   时间戳:', downloadResponse.data.timestamp);
      
      const data = downloadResponse.data.data;
      console.log('   下载的数据结构:');
      console.log('   - novels:', data.novels ? `${data.novels.length} 本` : '无');
      console.log('   - knowledgeDocuments:', data.knowledgeDocuments ? `${data.knowledgeDocuments.length} 个` : '无');
      console.log('   - characterCards:', data.characterCards ? `${data.characterCards.length} 个` : '无');
      console.log('   - userSettings:', data.userSettings ? '有' : '无');
      
      // 验证数据内容
      if (data.novels && data.novels.length > 0) {
        console.log('');
        console.log('📋 下载的小说详情:');
        data.novels.forEach((novel, index) => {
          console.log(`   ${index + 1}. ${novel.title} (${novel.id})`);
          console.log(`      作者: ${novel.author}`);
          console.log(`      内容: ${novel.content ? novel.content.substring(0, 50) + '...' : '无'}`);
        });
      }
      
      // 检查数据完整性
      const uploadedNovels = testData.novels;
      const downloadedNovels = data.novels || [];
      
      console.log('');
      console.log('🔍 数据完整性检查:');
      console.log(`   上传小说数量: ${uploadedNovels.length}`);
      console.log(`   下载小说数量: ${downloadedNovels.length}`);
      
      let matchCount = 0;
      uploadedNovels.forEach(uploadedNovel => {
        const found = downloadedNovels.find(downloadedNovel => 
          downloadedNovel.id === uploadedNovel.id && 
          downloadedNovel.title === uploadedNovel.title
        );
        if (found) {
          matchCount++;
          console.log(`   ✅ 找到匹配: ${uploadedNovel.title}`);
        } else {
          console.log(`   ❌ 未找到匹配: ${uploadedNovel.title}`);
        }
      });
      
      console.log(`   匹配率: ${matchCount}/${uploadedNovels.length} (${(matchCount/uploadedNovels.length*100).toFixed(1)}%)`);
      
      if (matchCount === uploadedNovels.length) {
        console.log('🎉 数据同步完整性验证通过！');
      } else {
        console.log('⚠️ 数据同步存在丢失或不匹配');
      }
      
    } else {
      console.log('❌ 数据下载失败:', downloadResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   请求URL:', error.config?.url);
    }
  }
}

// 测试不同用户的数据隔离
async function testUserDataIsolation() {
  console.log('\n=== 用户数据隔离测试 ===');
  
  try {
    // 使用不同的用户Token
    const user1Token = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    const user2Token = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyOTcxODJfaXZ5eThhZHIxIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    console.log('👤 用户1下载数据...');
    const user1Response = await axios.get(`${BASE_URL}/sync/download`, {
      headers: { 'Authorization': `Bearer ${user1Token}` }
    });
    
    console.log('👤 用户2下载数据...');
    const user2Response = await axios.get(`${BASE_URL}/sync/download`, {
      headers: { 'Authorization': `Bearer ${user2Token}` }
    });
    
    const user1Data = user1Response.data.success ? user1Response.data.data : {};
    const user2Data = user2Response.data.success ? user2Response.data.data : {};
    
    console.log(`用户1数据: 小说 ${(user1Data.novels || []).length} 本`);
    console.log(`用户2数据: 小说 ${(user2Data.novels || []).length} 本`);
    
    // 检查数据是否隔离
    const user1Novels = user1Data.novels || [];
    const user2Novels = user2Data.novels || [];
    
    let hasOverlap = false;
    user1Novels.forEach(novel1 => {
      const found = user2Novels.find(novel2 => novel2.id === novel1.id);
      if (found) {
        hasOverlap = true;
        console.log(`⚠️ 发现重复数据: ${novel1.title} (${novel1.id})`);
      }
    });
    
    if (!hasOverlap) {
      console.log('✅ 用户数据隔离正常');
    } else {
      console.log('❌ 用户数据隔离存在问题');
    }
    
  } catch (error) {
    console.error('❌ 用户隔离测试失败:', error.message);
  }
}

// 运行测试
async function main() {
  await testSyncFlow();
  await testUserDataIsolation();
  console.log('\n🎯 测试建议:');
  console.log('1. 如果数据完整性验证通过，说明服务器端同步功能正常');
  console.log('2. 如果Flutter应用登录后没有数据，检查是否调用了downloadFromCloud()');
  console.log('3. 确保登录成功后触发自动数据同步下载');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testSyncFlow, testUserDataIsolation };
