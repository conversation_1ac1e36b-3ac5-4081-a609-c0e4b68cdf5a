const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

async function testPackagesAPI() {
  console.log('=== 测试套餐API ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    console.log('📦 测试获取套餐列表...');
    const response = await axios.get(`${BASE_URL}/packages`);
    
    if (response.data.success) {
      console.log('✅ 获取套餐成功');
      console.log(`   套餐数量: ${response.data.data.length}`);
      
      response.data.data.forEach((pkg, index) => {
        console.log(`   ${index + 1}. ${pkg.name} (${pkg.id})`);
        console.log(`      价格: ¥${pkg.price}`);
        console.log(`      时长: ${pkg.durationDays === -1 ? '永久' : pkg.durationDays + '天'}`);
        console.log(`      状态: ${pkg.isActive ? '激活' : '禁用'}`);
        console.log(`      功能: ${pkg.features.join(', ')}`);
        console.log('');
      });
    } else {
      console.log('❌ 获取套餐失败:', response.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   请求URL:', error.config?.url);
    }
  }
}

// 运行测试
if (require.main === module) {
  testPackagesAPI().catch(console.error);
}

module.exports = { testPackagesAPI };
