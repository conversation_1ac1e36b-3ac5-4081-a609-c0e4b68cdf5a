#!/usr/bin/env node

const axios = require('axios');

// 腾讯云API基础URL
const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/api';

// 测试发送验证码
async function testSendCode(phoneNumber) {
  try {
    console.log(`\n📱 发送验证码到: ${phoneNumber}`);
    const response = await axios.post(`${BASE_URL}/auth/send-code`, {
      phoneNumber: phoneNumber
    });
    
    console.log('✅ 发送成功:', response.data);
    
    // 如果返回了调试验证码，返回它
    if (response.data.debugCode) {
      console.log(`🔑 验证码: ${response.data.debugCode} (开发环境)`);
      return response.data.debugCode;
    }
    
    return null;
  } catch (error) {
    console.log('❌ 发送失败:', error.response?.data || error.message);
    return null;
  }
}

// 测试验证验证码
async function testVerifyCode(phoneNumber, code) {
  try {
    console.log(`\n🔍 验证验证码: ${phoneNumber} - ${code}`);
    const response = await axios.post(`${BASE_URL}/auth/verify-code`, {
      phoneNumber: phoneNumber,
      code: code
    });
    
    console.log('✅ 验证成功:', response.data);
    return true;
  } catch (error) {
    console.log('❌ 验证失败:', error.response?.data || error.message);
    return false;
  }
}

// 测试用户注册
async function testRegister(phoneNumber, password, username, memberCode = null) {
  try {
    console.log(`\n👤 注册用户: ${username} (${phoneNumber})`);
    const response = await axios.post(`${BASE_URL}/auth/register`, {
      username: username,
      phoneNumber: phoneNumber,
      password: password,
      verificationCode: '123456', // 固定验证码
      memberCode: memberCode
    });
    
    console.log('✅ 注册成功:', response.data);
    return response.data;
  } catch (error) {
    console.log('❌ 注册失败:', error.response?.data || error.message);
    return null;
  }
}

// 测试用户登录
async function testLogin(username, password) {
  try {
    console.log(`\n🔐 用户登录: ${username}`);
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      username: username,
      password: password
    });
    
    console.log('✅ 登录成功:', response.data);
    return response.data;
  } catch (error) {
    console.log('❌ 登录失败:', error.response?.data || error.message);
    return null;
  }
}

// 完整的注册流程测试
async function testFullRegistrationFlow() {
  console.log('=== 完整注册流程测试 ===');
  
  const phoneNumber = '13800138000';
  const password = '123456';
  const username = 'testuser001';
  
  // 1. 发送验证码
  const verificationCode = await testSendCode(phoneNumber);
  if (!verificationCode) {
    console.log('❌ 无法获取验证码，测试终止');
    return;
  }
  
  // 2. 验证验证码
  const codeValid = await testVerifyCode(phoneNumber, verificationCode);
  if (!codeValid) {
    console.log('❌ 验证码验证失败，测试终止');
    return;
  }
  
  // 3. 注册用户（不使用会员码）
  const registerResult = await testRegister(phoneNumber, password, username);
  if (!registerResult) {
    console.log('❌ 注册失败，测试终止');
    return;
  }

  // 4. 测试登录
  const loginResult = await testLogin(username, password);
  if (!loginResult) {
    console.log('❌ 登录失败');
    return;
  }
  
  console.log('\n🎉 完整注册流程测试成功！');
}

// 测试会员码注册流程
async function testMemberCodeRegistrationFlow() {
  console.log('\n=== 会员码注册流程测试 ===');
  
  const phoneNumber = '13900139001';
  const password = '123456';
  const nickname = '会员用户';
  const memberCode = 'CLOUDTEST001'; // 使用之前创建的会员码
  
  // 1. 发送验证码
  const verificationCode = await testSendCode(phoneNumber);
  if (!verificationCode) {
    console.log('❌ 无法获取验证码，测试终止');
    return;
  }
  
  // 2. 验证验证码
  const codeValid = await testVerifyCode(phoneNumber, verificationCode);
  if (!codeValid) {
    console.log('❌ 验证码验证失败，测试终止');
    return;
  }
  
  // 3. 使用会员码注册用户
  const registerResult = await testRegister(phoneNumber, password, nickname, memberCode);
  if (!registerResult) {
    console.log('❌ 注册失败，测试终止');
    return;
  }
  
  // 检查会员状态
  if (registerResult.data && registerResult.data.user) {
    const user = registerResult.data.user;
    console.log(`\n👑 会员状态: ${user.membershipType}`);
    if (user.membershipExpireAt) {
      console.log(`⏰ 过期时间: ${user.membershipExpireAt}`);
    }
  }
  
  console.log('\n🎉 会员码注册流程测试成功！');
}

// 测试验证码过期
async function testCodeExpiration() {
  console.log('\n=== 验证码过期测试 ===');
  
  const phoneNumber = '13700137000';
  
  // 发送验证码
  const verificationCode = await testSendCode(phoneNumber);
  if (!verificationCode) {
    console.log('❌ 无法获取验证码，测试终止');
    return;
  }
  
  console.log('⏳ 等待验证码过期（实际应用中是5分钟，这里只是演示）...');
  
  // 尝试使用错误的验证码
  await testVerifyCode(phoneNumber, '000000');
  
  // 尝试使用正确的验证码
  await testVerifyCode(phoneNumber, verificationCode);
  
  console.log('\n✅ 验证码过期测试完成');
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  console.log('=== 腾讯云短信验证码测试工具 ===');
  console.log(`API地址: ${BASE_URL}`);
  
  switch (command) {
    case 'send':
      const phoneNumber = args[1] || '13800138000';
      await testSendCode(phoneNumber);
      break;
      
    case 'verify':
      const phone = args[1] || '13800138000';
      const code = args[2];
      if (!code) {
        console.log('请提供验证码');
        console.log('用法: node test-sms-verification.js verify <手机号> <验证码>');
        return;
      }
      await testVerifyCode(phone, code);
      break;
      
    case 'register':
      const regPhone = args[1] || '13800138000';
      const regPassword = args[2] || '123456';
      const regNickname = args[3] || '测试用户';
      const regMemberCode = args[4] || null;
      await testRegister(regPhone, regPassword, regNickname, regMemberCode);
      break;
      
    case 'login':
      const loginPhone = args[1] || '13800138000';
      const loginPassword = args[2] || '123456';
      await testLogin(loginPhone, loginPassword);
      break;
      
    case 'full':
      await testFullRegistrationFlow();
      break;
      
    case 'member':
      await testMemberCodeRegistrationFlow();
      break;
      
    case 'expire':
      await testCodeExpiration();
      break;
      
    case 'demo':
      console.log('\n=== 完整演示 ===');
      await testFullRegistrationFlow();
      await testMemberCodeRegistrationFlow();
      await testCodeExpiration();
      break;
      
    default:
      console.log('\n用法:');
      console.log('  node test-sms-verification.js send [手机号]                    - 发送验证码');
      console.log('  node test-sms-verification.js verify <手机号> <验证码>         - 验证验证码');
      console.log('  node test-sms-verification.js register <手机号> [密码] [昵称] [会员码] - 注册用户');
      console.log('  node test-sms-verification.js login <手机号> [密码]            - 用户登录');
      console.log('  node test-sms-verification.js full                           - 完整注册流程测试');
      console.log('  node test-sms-verification.js member                         - 会员码注册流程测试');
      console.log('  node test-sms-verification.js expire                         - 验证码过期测试');
      console.log('  node test-sms-verification.js demo                           - 运行所有演示');
      console.log('');
      console.log('示例:');
      console.log('  node test-sms-verification.js send 13800138000');
      console.log('  node test-sms-verification.js verify 13800138000 123456');
      console.log('  node test-sms-verification.js register 13800138000 123456 "我的昵称" CLOUDTEST001');
      console.log('  node test-sms-verification.js full');
      break;
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testSendCode,
  testVerifyCode,
  testRegister,
  testLogin,
  testFullRegistrationFlow,
  testMemberCodeRegistrationFlow
};
